# Base
VERSION := $(shell /bin/date "+%Y-%m-%d-%H-%M-%S")

# Service
SERVICE := c-check
STAGE := dev

# Docker
SERVICE_IMAGE := ${SERVICE}
PLATFORM := linux/amd64
PORT := 8003
TARGET_TAG := base
TARGET_TAGS := test develop # TARGET_TAGS in Dockerfile
RELEASE := false

# Service Resources
AWS_SECRETS := ${SERVICE}-aws-secret

##@ Helpers
.PHONY: help


help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST) && echo


##@ Operatrions


printenv: ## print default makefile variables
	@echo "SERVICE: ${SERVICE}"
	@echo "STAGE: ${STAGE}"
	@echo "PLATFORM: ${PLATFORM}"
	@echo "DOCKER_LOCATION: ${DOCKER_LOCATION}"
	@echo "PROJECT_ID: ${PROJECT_ID}"
	@echo "REPOSITORY: ${REPOSITORY}"
	@echo "CLOUD_RUN_SERVICE: ${CLOUD_RUN_SERVICE}"
	@echo "IMAGE_URI: ${IMAGE_URI}"
	@echo "PORT: ${PORT}"
	@echo "INGRESS: ${INGRESS}"
	@echo "NO_CACHE: ${NO_CACHE}"


clean-pycache: ## clean pycache
	@find . -type d -name __pycache__ -exec rm -rf {} \;
	@find . -type f -name '*.pyc' -exec rm -f {} \;

##@ Release


release: ## build docker image with target: release, (please remember to set RELEASE=true)
	DOCKER_BUILDKIT=1 docker build . -f ./Dockerfile \
	--progress=plain \
	--build-arg STAGE=${STAGE} \
	--build-arg RELEASE=${RELEASE} \
	--build-arg VERSION=${VERSION} \
	--platform ${PLATFORM} \
	--target release -t ${SERVICE_IMAGE}:release; \


##@ Config


configurations: ## Get configurations from gcp secrets
	@for SECRETS in ${AWS_SECRETS}; do \
		echo "Clean old config file: configs/$${SECRETS}.json"; \
		rm -rf configs/$${SECRETS}.json; \
		echo "Getting config file: configs/$${SECRETS}.json"; \
		gcloud secrets versions access latest --secret=$${SECRETS} --out-file=configs/$${SECRETS}.json; \
		echo "Config file: configs/$${SECRETS}.json, Done!!"; \
	done \

#c-update: ## Upload config files to gcp secrets
	#@for SECRETS in ${AWS_SECRETS}; do \
		#echo "Uploading config file: configs/$${SECRETS}.json"; \
		#gcloud secrets versions add $${SECRETS} --data-file=configs/$${SECRETS}.json; \
		#echo "Config file: configs/$${SECRETS}.json, Done!!"; \
	#done

#c-clean: ## Clean config files
	#@for SECRETS in ${AWS_SECRETS}; do \
		#echo "Clean config file: configs/$${SECRETS}.json"; \
		#rm -rf configs/$${SECRETS}.json; \
	#done


##@ Service Resources


grounding-dino: grounding-dino-clean ## Get grounding dino from gcp storage
	@echo "Getting grounding dino source code package from gcp bucket"
	@gsutil cp -r gs://c-check-builds/grounding_dino/source_codes/grounding_dino_precompiled.tar.gz .
	@echo "Getting grounding dino source code package from gcp bucket, Done!!"
	@echo "Unpacking grounding dino source code package"
	@mkdir -p ./dependencies/GroundingDINO
	@tar -xvf grounding_dino_precompiled.tar.gz -C ./dependencies
	@echo "Unpacking grounding dino source code package, Done!!"
	@echo "Remove tar file"
	@rm -rf grounding_dino_precompiled.tar.gz
	@echo "Remove tar file, Done!!"

grounding-dino-clean: ## Clean grounding dino
	@echo "Clean grounding dino"
	@rm -rf ./dependencies
	@echo "Clean grounding dino, Done!!"

grounding-dino-model: grounding-dino-model-clean ## Get grounding dino model from gcp storage
	@echo "Getting grounding dino model from gcp bucket"
	@gsutil cp -r gs://c-check-builds/grounding_dino/models/model.tar.gz ./groundingdino_model.tar.gz
	@echo "Getting grounding dino model from gcp bucket, Done!!"
	@echo "Unpacking grounding dino model"
	@mkdir -p ./groundingdino_model
	@tar -xvf groundingdino_model.tar.gz -C ./groundingdino_model
	@echo "Unpacking grounding dino model, Done!!"
	@echo "Remove tar file"
	@rm -rf ./groundingdino_model.tar.gz
	@echo "Remove tar file, Done!!"

grounding-dino-model-clean: ## Clean grounding dino model
	@echo "Clean grounding dino model"
	@rm -rf ./groundingdino_model.tar.gz
	@rm -rf ./groundingdino_model
	@echo "Clean grounding dino model, Done!!"

##@ Builds: Service docker images


devs-preparing: configurations grounding-dino grounding-dino-model ## preparing for devs

devs: ## build docker image with targets: ${TARGET_TAGS}
	@for TAG in $(TARGET_TAGS); do \
		DOCKER_BUILDKIT=1 docker build . -f ./Dockerfile \
		--progress=plain \
		--build-arg STAGE=${STAGE} \
		--build-arg RELEASE=${RELEASE} \
		--build-arg VERSION=${VERSION} \
		--platform ${PLATFORM} \
		--target $$TAG -t ${SERVICE_IMAGE}:$$TAG; \
	done


##@ Container Shell


container-shell: ## run docker image with command line: bash with given target
	@echo "Run /bin/bash in container with TARGET_TAG: ${TARGET_TAG}. TARGET_TAG: base develop"
	@docker run \
		-w /opt/dev \
		-v ${PWD}:/opt/dev \
		--rm -it ${SERVICE_IMAGE}:${TARGET_TAG} \
		/bin/bash

container-shell-gpu: ## run docker image with command line: bash with given target (gpu support)
	@echo "Run /bin/bash in container with TARGET_TAG: ${TARGET_TAG}. TARGET_TAG: base develop"
	@docker run \
		-w /opt/dev \
		-v ${PWD}:/opt/dev \
		--gpus all \
		--rm -it ${SERVICE_IMAGE}:${TARGET_TAG} \
		/bin/bash

container-release-bash: ## run docker image with command line: bash with target base
	@echo "Run /bin/bash in container with TARGET_TAG: ${TARGET_TAG}. TARGET_TAG: release"
	@docker run \
		--rm -it ${SERVICE_IMAGE}:release \
		/bin/bash

print-built-images: ## print built images
	@docker images | grep ${SERVICE}


##@ Clean


clean-built-images: ## clean built images
	@docker images | grep ${SERVICE} | awk '{print $$3}' | xargs docker rmi -f

clean-outputs: ## clean output directory
	@rm -rf outputs

clean-profilers: ## clean profilers directory
	@rm -rf profilers


##@ Launches


run-service: ## launch service with docker-compose file
	@docker compose -f ./docker-compose-${STAGE}.yml up -d --build

logs-service: ## show service logs with docker-compose file
	@docker compose -f ./docker-compose-${STAGE}.yml logs

down-service: ## stop service with docker-compose file
	@docker compose -f ./docker-compose-${STAGE}.yml down

restart-service: down-service run-service ## restart service with docker-compose file

run-release-service: ## launch service with docker-compose file
	@docker compose -f ./docker-compose.yml up -d --build

logs-release-service: ## show service logs with docker-compose file
	@docker compose -f ./docker-compose.yml logs

down-release-service: ## stop service with docker-compose file
	@docker compose -f ./docker-compose.yml down


##@ Benchmark


run-server-benchmark: ## run benchmark
	@docker run \
		-w /opt \
		-v ${PWD}/benchmark:/opt/benchmark \
		-v ${PWD}/outputs:/opt/outputs \
		-v ${PWD}/tmp/data_images:/opt/datas \
		-p 8089:8089 \
		--rm -it ${SERVICE_IMAGE}:develop \
		locust -f /opt/benchmark/locustfile.py -H http://localhost:8089;


##@ Lint


lint: ## py-lint
	@docker run \
		-w /opt/dev \
		-v ${PWD}:/opt/dev \
		--rm -it ${SERVICE_IMAGE}:develop \
		pylint src scripts


##@ TEST


testing: unit-testing end-to-end-testing integration-testing ## run all tests: unit, e2e, integration (Use 'make testing -j' for parallel execution)

unit-testing: ## run unit testing
	@docker run \
		-w /opt \
		-v ${PWD}/envs/local.env:/opt/.env \
		-v ${PWD}/app:/opt/app \
		-v ${PWD}/tests:/opt/tests \
        -v ${PWD}/logs:/opt/logs \
		--rm -it ${SERVICE_IMAGE}:test \
		pytest -vv tests/unit

integration-testing: ## run integration testing
	@docker run \
		-w /opt \
		-v ${HOME}/.config:/root/.config \
		-v ${PWD}/app:/opt/app \
		-v ${PWD}/envs/local.env:/opt/.env \
		-v ${PWD}/configs:/opt/configs \
		-v ${PWD}/logs:/opt/logs \
		-v ${PWD}/tests:/opt/tests \
		--rm -it ${SERVICE_IMAGE}:test \
		pytest -vv tests/integration

end-to-end-testing: ## run end-to-end testing
	@docker run \
		-w /opt \
		-v ${HOME}/.config:/root/.config \
		-v ${PWD}/app:/opt/app \
		-v ${PWD}/envs/local.env:/opt/.env \
		-v ${PWD}/configs:/opt/configs \
		-v ${PWD}/logs:/opt/logs \
		-v ${PWD}/tests:/opt/tests \
		--rm -it ${SERVICE_IMAGE}:test \
		pytest -vv tests/e2e

test-cov-report: ## run test coverage and generate html report
	@docker run \
		-w /opt \
		-e WORKSPACE=/opt \
		-v ${HOME}/.config:/root/.config \
		-v ${PWD}/.coveragerc:/opt/.coveragerc \
		-v ${PWD}/app:/opt/app \
		-v ${PWD}/envs/local.env:/opt/.env \
		-v ${PWD}/configs:/opt/configs \
		-v ${PWD}/logs:/opt/logs \
		-v ${PWD}/tests:/opt/tests \
		-v ${PWD}/htmlcov:/opt/htmlcov \
		--rm -i ${SERVICE_IMAGE}:develop \
		pytest -vv --cov=app tests --cov-report=html --cov-config="/opt/.coveragerc"

test-cov: ## run test coverage
	@docker run \
		-w /opt \
		-e WORKSPACE=/opt \
		-v ${HOME}/.config:/root/.config \
		-v ${PWD}/.coveragerc:/opt/.coveragerc \
		-v ${PWD}/app:/opt/app \
		-v ${PWD}/envs/local.env:/opt/.env \
		-v ${PWD}/configs:/opt/configs \
		-v ${PWD}/logs:/opt/logs \
		-v ${PWD}/tests:/opt/tests \
		--rm -i ${SERVICE_IMAGE}:develop \
		pytest -vv --cov=app tests --cov-config="/opt/.coveragerc"

test-env-shell: # (Not show on make -h) run shell in test container
	@docker run \
		-w /opt \
		-v ${HOME}/.config:/root/.config \
		-v ${PWD}/app:/opt/app \
		-v ${PWD}/envs/local.env:/opt/.env \
		-v ${PWD}/configs:/opt/configs \
		-v ${PWD}/tests:/opt/tests \
		-v ${PWD}/logs:/opt/logs \
		--rm -it ${SERVICE_IMAGE}:test \
		/bin/bash


###@ Profiling
#ITERATION := 1
#DEVELOP_IMAGE := ${SERVICE}:develop


#profiling: ## run profiling
	#@docker run \
		#-w /opt \
		#-v ${HOME}/.config:/root/.config \
		#-v ${PWD}/app:/opt/app \
		#-v ${PWD}/scripts:/opt/scripts \
		#--rm -it ${DEVELOP_IMAGE} \
		#python3 scripts/profile/profiler.py --iteration ${ITERATION}
