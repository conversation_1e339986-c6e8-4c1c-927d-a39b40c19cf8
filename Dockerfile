# From nvidia/cuda:11.7.1-devel-ubuntu22.04 AS base
FROM nvidia/cuda:12.3.1-devel-ubuntu22.04 AS base

# set environment variables
ENV AM_I_DOCKER=True
ENV BUILD_WITH_CUDA=0
ENV CUDA_HOME=/usr/local/cuda/
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install -y \
    libssl-dev \
    libffi-dev \
    ffmpeg \
    libsm6 \
    libxext6 \
    libjpeg-dev \
    libpng-dev \
    python3-pip \
    python3-dev \
    python-is-python3 \
    python3-venv


####################
# Builder image #
####################
FROM base AS builder

EN<PERSON> PIP_DEFAULT_TIMEOUT=100 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1

# RUN apt-get install --no-install-suggests --no-install-recommends --yes git
RUN pip install poetry==2.1.0 && poetry self add poetry-plugin-bundle

ENV PATH=/root/.local/bin:$PATH

ARG RELEASE

WORKDIR /builder

COPY ./pyproject.toml ./
COPY ./dependencies ./dependencies

COPY README.md ./

RUN if "$RELEASE" == "true" ; then \
    poetry bundle venv --python /usr/bin/python --only main /venv; \
    else \
    poetry bundle venv --python /usr/bin/python /venv; \
    fi

RUN /venv/bin/python3 -m ensurepip --upgrade

WORKDIR /builder/dependencies

#RUN git clone https://github.com/IDEA-Research/GroundingDINO.git

#RUN /venv/bin/python3 -m pip install -e GroundingDINO
#RUN /venv/bin/python3 -m pip install -e GroundingDINO
RUN cd GroundingDINO && /venv/bin/python3 -m pip install --no-build-isolation  -e .
RUN /venv/bin/python3 -m pip install xformers==0.0.23


######################
# Service Base Image #
######################
FROM base AS service_base

COPY --from=builder /venv /venv
COPY --from=builder /builder/dependencies /builder/dependencies

ENV PATH=/venv/bin:$PATH


#########################
# Service Testing Image #
#########################
FROM service_base AS test


#################
# Service Image #
#################
FROM service_base AS service

ARG STAGE VERSION

WORKDIR /opt
COPY ./configs /opt/configs
COPY ./envs/${STAGE}.env /opt/.env
COPY ./app /opt/app

ENV VERSION=${VERSION}


####################
# Development image #
####################
FROM service AS develop

CMD ["gunicorn", "app.main:app", "-w", "1", "-k", "uvicorn.workers.UvicornWorker", "--timeout", "3600", "-b", "0.0.0.0:8003", "--limit-request-line", "0", "--limit-request-field_size", "0", "--log-level", "debug"]


####################
# Release image #
####################
FROM service AS release

ENTRYPOINT ["gunicorn", "app.main:app", "-w", "1", "-k", "uvicorn.workers.UvicornWorker", "--timeout", "3600", "-b", "0.0.0.0:8003", "--limit-request-line", "0", "--limit-request-field_size", "0", "--log-level", "info"]
