STAGE=prod

LOG_PATH=${WORKSPACE}/logs

FEATURE_EXTRACTOR_MODEL_PATH=${WORKSPACE}/eva_clip_model/EVA02_CLIP_E_psz14_plus_s9B_trimmed.pt
IMAGE_SEGMENTER_MODEL_PATH=${WORKSPACE}/groundingdino_model/groundingdino_swint_ogc.pth

SQS_JSON='[
    {
        "stage": "Prod",
        "tag": "Facade",
        "url": "https://sqs.us-east-1.amazonaws.com/572111738119/c-extractor-critical-prod",
        "region": "us-east-1"
    },
    {
        "stage": "Prod",
        "tag": "Lens",
        "url": "https://sqs.us-east-1.amazonaws.com/572111738119/c-extractor-regular-prod",
        "region": "us-east-1"
    }
]'

REDIS_JSON='[
    {
        "stage": "Prod",
        "sourceHost": "c-check-source-prod-vvultq.serverless.use1.cache.amazonaws.com",
        "sourcePort": 6379,
        "featureHost": "c-check-prod-vvultq.serverless.use1.cache.amazonaws.com",
        "featurePort": 6379
    }
]'
