STAGE=local

# Empty values to disable loading models
FEATURE_EXTRACTOR_MODEL_PATH=
IMAGE_SEGMENTER_MODEL_PATH=

SQS_JSON='[
    {
        "stage": "Dev",
        "tag": "Facade",
        "url": "https://sqs.us-east-1.amazonaws.com/572111738119/c-extractor-critical-dev",
        "region": "us-east-1"
    },
    {
        "stage": "Dev",
        "tag": "Lens",
        "url": "https://sqs.us-east-1.amazonaws.com/572111738119/c-extractor-regular-dev",
        "region": "us-east-1"
    }
]'

REDIS_JSON='[
    {
        "stage": "Dev",
        "sourceHost": "c-check-source-dev-vvultq.serverless.use1.cache.amazonaws.com",
        "sourcePort": 6379,
        "featureHost": "c-check-dev-vvultq.serverless.use1.cache.amazonaws.com",
        "featurePort": 6379
    }
]'
