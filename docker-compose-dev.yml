# To be confirmed. No actual usage. The latest Dockerfile exists in the CD pipeline.
#
#version: '3.7'
#
#services:
#  c-check-dev:
#    image: c-check:develop
#    container_name: c-check-dev
#    platform: linux/amd64
#    deploy:
#      resources:
#        reservations:
#          devices:
#            - driver: nvidia
#              count: 1
#              capabilities: [gpu]
#    environment:
#      - WORKSPACE=/opt
#    command: >
#      gunicorn app.main:app -w 2 -k uvicorn.workers.UvicornWorker --timeout 3600 -b 0.0.0.0:8003 --limit-request-line 0 --limit-request-field_size 0 --log-level debug -c /opt/gunicorn_conf.py
#    volumes:
#      - ${HOME}/.config:/root/.config
#      - ./app:/opt/app
#      - ./envs/dev.env:/opt/.env
#      - ./configs:/opt/configs
#      - ./tests:/opt/tests
#      - ./gpu_memory_recoeds:/opt/gpu_memory_records
#      - ./gunicorn_conf.py:/opt/gunicorn_conf.py
#      - /mnt/faiss_indices:/opt/faiss_indices
#      - /mnt/groundingdino_model:/opt/groundingdino_model
#      - /mnt/eva_clip_model:/opt/eva_clip_model
#      - /var/log/provenance-c-check:/opt/logs
#    ports:
#      - "8003:8003"
#    networks:
#      - dev
#
#networks:
#  dev:
