## !! Maybe outdated. !!

## Descriptions

This project is a web service that provides APIs to check if the given image is copy-right protected image from faiss indices or not.
It has been developed using the Python web framework [Fastapi.](https://fastapi.tiangolo.com/lo/)

## Preparing

Firstly, make sure you have installed google-cloud-sdk - [installation](https://formulae.brew.sh/cask/google-cloud-sdk). And then, you can run the following command to login with your GCP account and set the default project:

    # Login with your GCP account to initiates the authentication process.
    $ gcloud auth login

    # Login with your GCP account to authorize your local application to access Google Cloud services using your user credentials.
    $ gcloud auth application-default login

    # Set the default project
    $ gcloud config set project <project_id>

    # Displays the current configuration settings for your gcloud command-line tool.
    $ gcloud config list

Please verify that your GCP account in the 'provenance-a100' project has sufficient permissions to execute GCP monitoring and instance-related APIs. 
If, when running this service, you encounter Google permission issues while making API calls, please request assistance from the DevOps team to grant the necessary permissions. 
The simplest approach would be to directly add 'editor' permissions for the developers.

Secondly, the service is built using Docker and Docker Compose. And this service is designed to be run in a containerized environment with GPU support.
Please make sure that you have installed Docker and Docker Compose on your development machine. Alos make sure that you have installed nvidia-docker2 to run the service with GPU support.

If you wnat to launch the service on a new GPU instance, you can request assistance from the DevOps team to prepare the necessary environment for running the service.

Ref: https://medium.com/%E5%B7%A5%E7%A8%8B%E9%9A%A8%E5%AF%AB%E7%AD%86%E8%A8%98/docker-%E5%BB%BA%E7%AB%8B-cuda-%E5%8F%8A-cudnn-%E7%92%B0%E5%A2%83-2d0684b16df3

### Make Commands

To build the service, this project provides a set of Makefile commands that you can use during development.
To view the available commands and their descriptions, you can run "make" or "make help" in your terminal.

    $ make
    or
    $ make help

In next sections, it will show you how to use these commands to build and run the service.

## Build and Run the Service

To build and run the service, you will need to prepare the following things first:
* Configuration: It includes the access key of AWS.
* Grounding Dino Source and Model: It includes the grounding dino source files and the grounding dino model files.

You can just run the following command to prepare these things:

    $ cd <path_to_project_root>
    $ make devs-preparing # It will prepare the configuration, db sources, grounding dino source and model, and faiss indices.

The following sections will show you these things in detail.

### Configuration

You can run the following command to get the configuration file from GCP secret-manager:

    $ make configurations

### Grounding Dino Source and Model

In this step, you need to download the grounding dino source and model from GCP storage (gs://c-check-builds/grounding_dino/).
Run the following command to download the grounding dino source and then unzip the file: :

    $ make grounding-dino

And then, you can run the following command to download the grounding dino model and then unzip the file:

    $ make grounding-dino-model

After running the previously mentioned commands, you will have the following folders in the project root:
* dependencies/GroundingDINO: It includes the grounding dino source files.
* groundingdino_model: It includes the grounding dino model files.

## Build Service Docker Images

After preparing the necessary things, you can run the following command to build the Docker images for the service:

    $ cd <path_to_project_root>
    $ make devs

By running this command, you will initiate the build process for Docker images, specifically targeting the tags: `base` and `develop`.
- The `base` tag refer to the base image with essential dependencies.
- The `develop` tag include additional tools, 3rd-party python packages and configurations required for development.

## Launch / Stop Service Locally

To run the service, you can run the following command:

    $ make run-service

The servie will host on the port ``8003`` by default.

After running the previously mentioned commands and successfully deploying the service,

you can open a web browser and navigate to `http://localhost:8003/docs` to access the Swagger UI of the service.

If you want to stop the service, you can run the following command:

    $ make down-service

## Testing

### Run Testing Within Service Container (In Docker)

To run the testing in the project, you can run the following command:

    $ make testing

This command will run the following testing:
* Unit Testing, make command: `make unit-testing`
* Integration Testing, make command: `make integration-testing`
* End-to-End Testing, make command: `make end-to-end-testing`

#### Testing with Coverage

To run the testing with coverage in the project, you can run the following command:

    $ make test-cov-report

This command will run the testing with coverage and generate the coverage report in the folder: `htmlcov`.

### Run Testing Without Service Container (In PyCharm)

If you want to run the testing in PyCharm, you can follow the steps below:

* Open the project in PyCharm.
* Switch to the python interpreter (with poetry) in the project.
* Install the necessary dependencies in the project.
    * Open the terminal in PyCharm.
    * Run the command to install the dependency: GroundingDINO: `cd dependencies/GroundingDINO && python3 setup.py develop`
        * Hint: The GroundingDINO is a dependency of the project. You need to install it before running the testing. This currently can only be installed manually.
        * Please make sure that you have downloaded the grounding dino source (`make grounding-dino`) before installing the dependency.

After installing the necessary dependencies, you can run the testing in PyCharm.
Just run ` pytest -vv --cov=app tests --cov-config=.coveragerc` in the terminal in PyCharm.

Or you can run the testing in PyCharm by following the steps below:
* Firstly, move to tests  folder and modify the configuration of running Make sure your working directory is correct. For this project, the working directory is ``/path/to/provenance-c-check-extractor``
* Then, you can run the testing by Run `pytest in tests` or Debug `pytest` in tests on tests.

## Others

### Logs

You can view the logs of the service by running the following command:

    $ tail -f logs/app.log

### Linting

To check the code quality in the project using pylint, you can run the following command:

    $ make lint

## Not Ready

### Development

Once you have built the necessary Docker images for the project, you can run the following command to start a container specifically for:

### Testing

To run the unit tests in the project, you can run the following command:

    $ make test

## Release

### Build Release Docker Images

In this project, I package the service into a Docker image with tag: `release`. You can run the following command to build the release image:

    $ make release

This command will build a Docker image with the tag: `release`.

### Launch Release Service Locally

Once the release image is built, you can run the following command to start a container for the release service locally:

    $ make run

Same as the development service, you can open a web browser and navigate to `http://localhost:8002/docs` to access the Swagger UI of the service.

To stop the service, you can run the following command:

    $ make down

### Benchmark

In this project, I use the Python library Locust to build the benchmark. You can initiate the benchmark by executing the following command:

    $ make benchmark

Once the benchmark is running, you can open a web browser and visit `http://localhost:8089/` to begin the benchmarking process.

### Profiling

In this project, I use the Python library fastapi-profiler and memory-profiler to build the profiling.

## Reference

* https://cloud.google.com/run/docs/configuring/services/cpu
* https://console.cloud.google.com/gcr/images/deeplearning-platform-release

### Google
- source: https://drive.google.com/drive/folders/1GyiPpHSoSCPiDiyJM2VaRMJdsQo4vCZG
- manifest: https://docs.google.com/spreadsheets/d/1K-Pb8Ye2Mc5funidfPGz1W-6StI3FuFcP_VcuCvMq7w

### MongoDB
- mongodb+srv://dev:<EMAIL>/

### AWS IAM
- service account: https://us-east-1.console.aws.amazon.com/iam/home?region=us-east-1#/users/details/provenance-dev?section=permissions
- permission: https://us-east-1.console.aws.amazon.com/iam/home?region=us-east-1#/users/details/provenance-dev/editPolicy/s3-one-bucket-provenance?step=addPermissions

### AWS S3
- source video: https://s3.console.aws.amazon.com/s3/buckets/provenance-c-check-source
- output frame: https://s3.console.aws.amazon.com/s3/buckets/provenance-c-check-output
