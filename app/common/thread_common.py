import queue
import threading
from concurrent.futures import Future
from functools import wraps


class PrioritizedTask:
    def __init__(self, priority, func, args=(), kwargs=None):
        self.priority = priority
        self.func = func
        self.args = args
        self.kwargs = kwargs or {}
        self.future = Future()

    def __lt__(self, other):
        return self.priority < other.priority

    def run(self):
        if not self.future.set_running_or_notify_cancel():
            return
        try:
            result = self.func(*self.args, **self.kwargs)
            self.future.set_result(result)
        except Exception as e:
            self.future.set_exception(e)


class PrioritizedThreadPoolExecutor:
    def __init__(self, max_workers):
        self.task_queue = queue.PriorityQueue()
        self.threads = []
        self.shutdown_flag = threading.Event()

        for _ in range(max_workers):
            thread = threading.Thread(target=self._worker)
            thread.daemon = True
            thread.start()
            self.threads.append(thread)

    def _worker(self):
        while not self.shutdown_flag.is_set():
            try:
                task = self.task_queue.get(timeout=0.1)
                task.run()
                self.task_queue.task_done()
            except queue.Empty:
                continue

    def submit(self, priority, func, *args, **kwargs):
        task = PrioritizedTask(priority, func, args, kwargs)
        self.task_queue.put(task)
        return task.future

    def shutdown(self, wait=True):
        self.shutdown_flag.set()
        if wait:
            for thread in self.threads:
                thread.join()

    @staticmethod
    def create(max_workers) -> 'PrioritizedThreadPoolExecutor':
        return PrioritizedThreadPoolExecutor(max_workers)


class ThreadSafeCounter:
    def __init__(self, initial=0):
        self.value = initial
        self.lock = threading.Lock()

    def increment(self):
        with self.lock:
            self.value += 1

    def decrement(self):
        with self.lock:
            self.value -= 1

    def get(self) -> int:
        with self.lock:
            return self.value


def synchronized(lock):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            with lock:
                return func(*args, **kwargs)

        return wrapper

    return decorator
