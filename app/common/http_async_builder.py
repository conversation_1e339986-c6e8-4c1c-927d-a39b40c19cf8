import time

import httpx
from httpx import Response, Timeout, HTTPStatusError

from app.common.logger_common import logger, log_exception

_DEFAULT_TIMEOUT = httpx.Timeout(connect=10.0, read=20.0, write=10.0, pool=10.0)
# No read timeout, and nothing to write actually
_EXTENDED_TIMEOUT = httpx.Timeout(connect=300.0, read=None, write=10.0, pool=None)


class HttpAsyncBuilder:
    httpx_client: httpx.AsyncClient = httpx.AsyncClient()

    def __init__(self, url: str, tag: str = 'NoTag'):
        self._url: str = url
        self._tag: str = tag
        self._headers: dict | None = None
        self._files: dict | None = None
        self._data: dict | None = None
        self._payload: dict | None = None
        self._timeout: Timeout = _DEFAULT_TIMEOUT
        self._follow_redirects: bool = False

    @classmethod
    async def on_shutdown(cls):
        await cls.httpx_client.aclose()

    def set_headers(self, headers: dict):
        self._headers = headers
        return self

    def set_files(self, files: dict):
        self._files = files
        return self

    def set_data(self, data: dict):
        self._data = data
        return self

    def set_payload(self, payload: dict):
        self._payload = payload
        return self

    def extend_timeout(self):
        self._timeout = _EXTENDED_TIMEOUT
        return self

    def enable_follow_redirects(self):
        self._follow_redirects = True
        return self

    async def execute_core(self) -> Response:
        if self._files is not None or self._data is not None or self._payload is not None:
            method = 'AsyncPOST'
            response = await self.httpx_client.post(
                self._url, headers=self._headers, timeout=self._timeout,
                files=self._files, data=self._data, json=self._payload
            )
        else:
            method = 'AsyncGET'
            response = await self.httpx_client.get(
                self._url, headers=self._headers, timeout=self._timeout,
                follow_redirects=self._follow_redirects
            )

        if self._tag is not None:
            logger.info(
                ' | '.join([
                    self._tag,
                    method,
                    f'{response.elapsed.total_seconds():.3f}s',
                    self._url
                ])
            )

        return response

    async def execute(self) -> Response:
        status_code = -1
        try:
            response = await self.execute_core()
            status_code = response.status_code
            response.raise_for_status()
            return response

        except HTTPStatusError as e:
            log_exception(f'{self._tag} | HTTP_{status_code} | {self._url}')
            return Response(status_code=e.response.status_code, text=str(e))

        except Exception as e:
            log_exception(f'{self._tag} | HTTP_X | {self._url}')
            return Response(status_code=500, text=str(e))

    async def execute_periodically(
            self,
            pass_condition: callable,
            fail_condition: callable,
            tries: int = 30,
            interval: int = 1,
    ) -> Response:
        while True:
            result = await self.execute()

            if pass_condition(result) or fail_condition(result) or tries <= 0:
                break

            time.sleep(interval)
            tries -= 1

        return result
