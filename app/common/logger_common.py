import asyncio
import datetime
import functools
import inspect
import logging
import sys
import time
import traceback
from contextvars import ContextVar

logger = logging.getLogger('main.app')


def log_exception(tag: str, level_func: callable = logger.error):
    segments = [tag]

    stacks = inspect.stack()[1:]
    for frame in stacks:
        frame: inspect.FrameInfo
        if '/app/' in frame.filename:
            app_file = frame.filename.split('/app/')[-1]
            segments.append(f'{app_file}::{frame.function}::{frame.lineno}')
        else:
            break

    exc_type, exc_value, _ = sys.exc_info()
    was_exception = exc_type is not None
    if was_exception:
        segments.insert(1, f'{exc_type.__module__}.{exc_type.__name__}')
        segments.insert(2, str(exc_value) if exc_value else 'N/A')

    level_func(' | '.join(segments))

    if was_exception:
        level_func(traceback.format_exc().strip())


def to_iso_time(t: float) -> str:
    return time.strftime('%Y-%m-%dT%H:%M:%S', time.gmtime(t))


def to_utc_iso_time_ms(t: float) -> str:
    return datetime.datetime.fromtimestamp(t, tz=datetime.timezone.utc).isoformat()


def to_elapsed_seconds(t: float, fraction_digits: int = 3) -> str:
    return f'elapsed {round(t, fraction_digits)}s'


def timed_log(
        tag: str, start_ts: float,
        fixed_tag: str | None = '@timed',
        extra_data: str | None = None,
        level_func: callable = logger.info,
):
    elapsed = time.perf_counter() - start_ts
    elapsed_str = to_elapsed_seconds(elapsed)

    if fixed_tag:
        tag = f'{fixed_tag} | {tag}'

    line = f'{tag} | {elapsed_str}' if extra_data is None else f'{tag} | {elapsed_str} | {extra_data}'
    level_func(line)


def timed(
        _func: callable = None,
        *,
        tag: str = None,
        log_start: bool = False,
        level_func: callable = logger.info
):
    def decorator(func):
        if tag:
            _tag = func.__qualname__ + '|' + tag
        else:
            _tag = func.__qualname__

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            if log_start:
                level_func(f'{_tag} | start')
            start_ts = time.perf_counter()
            result = await func(*args, **kwargs)
            timed_log(_tag, start_ts, level_func=level_func)
            return result

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            if log_start:
                level_func(f'{_tag} | start')
            start_ts = time.perf_counter()
            result = func(*args, **kwargs)
            timed_log(_tag, start_ts, level_func=level_func)
            return result

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    if _func is None:
        return decorator
    else:
        return decorator(_func)


class TimedContext:
    TIMED_CONTEXT_TAG = 'timed_ctx_tag'
    TIMED_CONTEXT_DEFAULT_VALUE = ''
    tag: ContextVar[str] = ContextVar(TIMED_CONTEXT_TAG, default=TIMED_CONTEXT_DEFAULT_VALUE)

    @staticmethod
    def log(data: str, level_func: callable = logger.info):
        level_func(f'{TimedContext.tag.get()} | {data}')


def timed_context(func):
    def _cxt_timed_log(kwargs, start_ts):
        tag1 = TimedContext.tag.get()
        tag2 = kwargs.get('_alt_ctx_tag', func.__name__)
        timed_log(f'{tag1} | {tag2}', start_ts, fixed_tag=None, level_func=logger.info)

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_ts = time.perf_counter()
        result = await func(*args, **kwargs)
        _cxt_timed_log(kwargs, start_ts)
        return result

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_ts = time.perf_counter()
        result = func(*args, **kwargs)
        _cxt_timed_log(kwargs, start_ts)
        return result

    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
