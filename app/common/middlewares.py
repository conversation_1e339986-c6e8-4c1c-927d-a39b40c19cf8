from contextvars import Con<PERSON>V<PERSON>

from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request

from app.common.logger_common import logger

DEFAULT_CTX_VALUE = 'N/A'
TRACE_ID_CTX_KEY = 'X-B3-TraceId'
SPAN_ID_CTX_KEY = 'X-B3-SpanId'

_trace_id_ctx_var: ContextVar[str] = ContextVar(TRACE_ID_CTX_KEY, default=DEFAULT_CTX_VALUE)
_span_id_ctx_var: ContextVar[str] = ContextVar(SPAN_ID_CTX_KEY, default=DEFAULT_CTX_VALUE)


def get_trace_id() -> str:
    return _trace_id_ctx_var.get()


def get_span_id() -> str:
    return _span_id_ctx_var.get()


class RequestContextLogMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        trace_id = _trace_id_ctx_var.set(request.headers.get(TRACE_ID_CTX_KEY, DEFAULT_CTX_VALUE))
        span_id = _span_id_ctx_var.set(request.headers.get(SPAN_ID_CTX_KEY, DEFAULT_CTX_VALUE))

        response = await call_next(request)
        response.headers[TRACE_ID_CTX_KEY] = get_trace_id()
        response.headers[SPAN_ID_CTX_KEY] = get_span_id()

        _trace_id_ctx_var.reset(trace_id)
        _span_id_ctx_var.reset(span_id)

        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        headers = {k: v for k, v in request.headers.items()}
        logger.info(f'InRequest: {request.method}, {request.url}, {headers}')
        response = await call_next(request)

        logger.info(f'OutResponse: {request.method}, {request.url}, {response.status_code}')
        return response
