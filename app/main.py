import logging
import sys

from fastapi import <PERSON><PERSON><PERSON>, Request, Response, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi_profiler import PyInstrumentProfilerMiddleware

from app import get_settings
from app.common.middlewares import RequestContextLogMiddleware
from app.exceptions.exception_handler import ExceptionHandler
from app.exceptions.service_exception import ServiceException

settings = get_settings()

logger = logging.getLogger('main.app')

app = FastAPI(
    title='C-Extractor Service',
    description='C-Extractor Service APIs',
    version=settings.ver,
    docs_url='/docs',
    redoc_url='/redoc',
    openapi_url='/openapi.json',
    swagger_ui_parameters={'syntaxHighlight': False},
)


async def on_startup():
    from app.services.message_runner import MessageRunner
    MessageRunner.start()
    logger.info('App startup')


async def on_shutdown():
    from app.services.message_runner import MessageRunner
    MessageRunner.stop()
    from app.common.http_async_builder import HttpAs<PERSON><PERSON>uilder
    await HttpAsyncBuilder.on_shutdown()
    logger.info('App shutdown')


app.add_event_handler('startup', on_startup)
app.add_event_handler('shutdown', on_shutdown)
app.add_exception_handler(RequestValidationError, ExceptionHandler.validation_exception_handler)
app.add_exception_handler(HTTPException, ExceptionHandler.http_request_exception_handler)
app.add_exception_handler(ServiceException, ExceptionHandler.service_exception_handler)
app.add_exception_handler(Exception, ExceptionHandler.unhandled_exception_handler)
app.add_middleware(RequestContextLogMiddleware)

ALLOWED_ORIGINS = '*'  # or 'foo.com', etc.


# handle CORS preflight requests
@app.options('/{rest_of_path:path}')
async def preflight_handler() -> Response:
    response = Response()
    response.headers['Access-Control-Allow-Origin'] = ALLOWED_ORIGINS
    response.headers['Access-Control-Allow-Methods'] = 'POST, GET, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'
    return response


# set CORS headers
@app.middleware('http')
async def add_cors_header(request: Request, call_next):
    response = await call_next(request)
    response.headers['Access-Control-Allow-Origin'] = ALLOWED_ORIGINS
    response.headers['Access-Control-Allow-Methods'] = 'POST, GET, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type'
    return response


if settings.profile_enabled:
    app.add_middleware(
        PyInstrumentProfilerMiddleware,
        server_app=app,
        profiler_output_type='html',
        is_print_each_request=False,
        open_in_browser=False,
        html_file_name=f'{settings.workspace}/profiles/profile.html',
    )


@app.get('/chk')
async def health_check() -> dict:
    return {
        'settings': settings.dict()
    }


# main
if __name__ == '__main__':
    import uvicorn

    logger.info(f'Running Python version: {sys.version}')
    logger.info(f'Running Uvicorn version: {uvicorn.__version__}')

    uvicorn.run(
        app,
        host='0.0.0.0',
        port=8003,
        log_level=settings.log_level.lower(),
        timeout_keep_alive=3600,
    )
