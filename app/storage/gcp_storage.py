from app.common.logger_common import log_exception
from app.storage.base_cloud_storage import BaseCloudStorage, get_mimetype
from app.utils.lrucache import get_gcp_storage_client

GCP_STORAGE_CLIENT = get_gcp_storage_client()


class GcpStorage(BaseCloudStorage):
    def __init__(self, bucket_name):
        self.bucket_name = bucket_name
        self.bucket_client = GCP_STORAGE_CLIENT.get_bucket(bucket_name)

    def upload_data(self, path: str, data: bytes) -> bool:
        try:
            blob = self.bucket_client.blob(path)
            blob.upload_from_string(data=data, content_type=get_mimetype(path))
            return True

        except Exception as e:
            log_exception(tag=f'UploadGcp | {self.bucket_name}/{path}')
            return False

    def download_data(self, path: str) -> bytes | None:
        try:
            blob = self.bucket_client.blob(path)
            return blob.download_as_bytes()

        except Exception as e:
            log_exception(tag=f'DownloadGcp | {self.bucket_name}/{path}')
            return None

    def to_uri(self, path: str) -> str:
        return f'gs://{self.bucket_name}/{path}'
