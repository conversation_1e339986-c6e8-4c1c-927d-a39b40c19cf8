from app.common.logger_common import log_exception
from app.storage.base_cloud_storage import BaseCloudStorage, get_mimetype
from app.utils.lrucache import get_aws_s3_client

AWS_S3_CLIENT = get_aws_s3_client()


class S3Storage(BaseCloudStorage):
    def __init__(self, bucket_name):
        self.bucket_name = bucket_name
        self.s3_client = AWS_S3_CLIENT

    def upload_data(self, path: str, data: bytes) -> bool:
        try:
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=path,
                Body=data,
                ContentType=get_mimetype(path)
            )
            return True
        except Exception as e:
            log_exception(tag=f'UploadAws | {self.bucket_name}/{path}')
            return False

    def download_data(self, path: str) -> bytes | None:
        try:
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=path)
            data = response['Body'].read()
            return data

        except Exception as e:
            log_exception(tag=f'DownloadAws | {self.bucket_name}/{path}')
            return None

    def to_uri(self, path: str) -> str:
        return f's3://{self.bucket_name}/{path}'
