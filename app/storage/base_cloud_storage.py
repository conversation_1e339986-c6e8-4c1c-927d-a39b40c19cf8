import asyncio
import logging
import mimetypes
from abc import ABC, abstractmethod

logger = logging.getLogger("main.app")


def get_mimetype(path: str) -> str:
    guess = mimetypes.guess_type(path)
    if guess and guess[0]:
        return guess[0]
    else:
        logger.warning(f'UnknownMimetype | {path}')
        return 'application/octet-stream'


def add_mimetype(ext: str, mimetype: str) -> str | None:
    return mimetypes.add_type(mimetype, ext)


# Python 3.10 or older misses the webp mimetype
add_mimetype('.webp', 'image/webp')


class BaseCloudStorage(ABC):
    @abstractmethod
    def download_data(self, path: str) -> bytes | None: pass

    @abstractmethod
    def upload_data(self, path: str, data: bytes) -> bool: pass

    @abstractmethod
    def to_uri(self, path: str) -> str: pass

    def upload_no_wait(self, path: str, data: bytes):
        asyncio.create_task(asyncio.to_thread(
            self.upload_data, path, data
        ))

    async def download_async(self, path: str) -> bytes | None:
        return await asyncio.to_thread(self.download_data, path)
