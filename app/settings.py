import asyncio
import json
import os

import boto3
from fastapi_camelcase import CamelModel
from pydantic_settings import BaseSettings, SettingsConfigDict
from redis.asyncio import Redis

from app.common.logger_common import log_exception


class Settings(BaseSettings):
    stage: str = 'dev'
    workspace: str = '/opt'

    ver: str = os.environ.get('VERSION', 'local')

    log_level: str = 'INFO'
    log_format: str = '%(levelname)s %(asctime)s | %(message)s'
    log_path: str = ''
    log_in_json: bool = False

    profile_enabled: bool = False

    feature_extractor_model_path: str = '/opt/eva_clip_model/eva02_clip_e_psz14_plus_s9b_trimmed.pt'
    image_segmenter_model_id: str = 'IDEA-Research/grounding-dino-base'

    gpu_memory_record_enabled: bool = False

    sqs_json: str = ''
    redis_json: str = ''

    model_config = SettingsConfigDict(
        env_file='/opt/.env',
        env_file_encoding='utf-8',
        extra='allow'
    )

    provenance_envs: list[str] = [
        'provenance.ai',
        'provenance.cool',
        'provenancenow.xyz',
        'provenancedata.pro',
        'provenancedata.tech',
        'provenance.local.dev',
        'c-check.external.api.prod',
        'c-check.external.api.dev',
        'c-check.external.api.local',
    ]

    # Cloud-based features are lower priority. Limit it and yield the resources for AIGC.
    max_lens_requests: int = 50

    @property
    def is_local(self):
        return self.stage.lower() == 'local'


class SqsConfig(CamelModel):
    stage: str
    tag: str
    url: str
    region: str


class SqsConnector:
    def __init__(self, stage: str, tag: str, url: str, client: boto3.client):
        self.stage = stage
        self.tag = tag
        self.url = url
        self.client = client

    @property
    def log_tag(self):
        return self.stage + self.tag

    def receive_message(self):
        try:
            return self.client.receive_message(
                QueueUrl=self.url,
                MaxNumberOfMessages=1,
                WaitTimeSeconds=20
            )
        except Exception:
            log_exception('SQSReceive')
            return {'Messages': []}

    def delete_message(self, receipt_handle):
        try:
            return self.client.delete_message(
                QueueUrl=self.url,
                ReceiptHandle=receipt_handle
            )
        except Exception:
            log_exception('SQSDelete')


class RedisConfig(CamelModel):
    stage: str
    source_host: str
    source_port: int
    feature_host: str
    feature_port: int


class RedisConnector:
    def __init__(self, stage: str, source_client: Redis, feature_client: Redis):
        self.stage = stage
        self.source_client = source_client
        self.feature_client = feature_client

    async def get_source_binary_message(self, uri: str) -> bytes:
        try:
            key = uri.removeprefix('redis://')
            return await self.source_client.get(key)
        except Exception:
            log_exception('RedisReadBinary')
            return b''

    async def write_message(self, msg_id: str, msg_ttl_sec: int, data: dict):
        try:
            json_str = json.dumps(data)
            await self.feature_client.setex(
                name=msg_id,
                time=msg_ttl_sec,
                value=json_str
            )
        except Exception:
            log_exception('RedisWrite')
