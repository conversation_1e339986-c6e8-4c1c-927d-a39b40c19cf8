<!DOCTYPE html>
<html lang="en">
<head>
    <title>C-Check Service</title>
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@1,700" rel="stylesheet">
    <style>
        h2 {
            font-family: sans-serif;
            margin: 8px 2px;
        }

        table {
            font-family: arial, sans-serif;
            border-collapse: collapse;
            width: 100%;
        }

        td, th {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
        }

        tr:nth-child(even) {
            background-color: #dddddd;
        }

        .btn, input::file-selector-button {
            background-color: #666;
            border: none;
            color: white;
            padding: .5em 2em;
            text-align: center;
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .btn:hover, input:hover::file-selector-button {
            background-color: #999;
        }

        #submit_image_c_check_button {
            display: block;
            margin: 12px auto;
            padding: 8px 100px;
            font-size: 16px;
            font-family: sans-serif;
        }


        #drop_zone {
            background-color: #ddd;
            border: 2px solid #666;
            outline: 1px dashed #999;
            outline-offset: -8px;

            transition:
                    background-color 0.2s ease,
                    outline-offset 0.2s ease;
        }

        #drop_zone.dragging {
            background-color: #eee;
            outline-offset: -14px;
        }

        .box {
            display: flex;
            position: relative;
            width: 80vw;
            height: 24vh;
            margin: 0 auto;
            box-sizing: border-box;
            justify-content: center;
            text-align: center;
        }

        .box .box__icon {
            width: 40px;
            height: 100%;
            fill: #999;
        }

        .box .box__file {
            display: none;
        }

        .box label {
            display: flex;
            position: relative;
            margin: 8vh 30px;
            padding: 20px;
            max-width: 80%;
            align-items: center;
            font-family: sans-serif;
            font-size: 16px;
        }

        .box label:hover {
            color: #666;
        }

        .box .box__img {
            display: block;
            background-color: #dddc;
            width: 0;
            height: 80%;
            margin: 0;
            object-fit: contain;
            object-position: center;
            border: 0;

            opacity: 1;
            transition:
                    opacity 0.2s ease,
                    width 0.2s ease;
        }

        .box .box__img.active {
            margin: auto 20px;
            width: 20vw;
        }

        .box.dragging .box__img {
            opacity: 0.2;
        }

        #loader-container {
            pointer-events: none;
            opacity: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 1000;
            transition: opacity 0.2s ease;
        }

        #loader-container.active {
            pointer-events: all;
            opacity: 1;
        }

        .loader {
            width: 48px;
            height: 48px;
            background: #999;
            display: inline-block;
            border-radius: 50%;
            box-sizing: border-box;
            animation: animloader 1s ease-in infinite;
        }

        @keyframes animloader {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        .fullscreen-center {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }


        .gallery {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
        }

        .gallery__group {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin: 20px 0;
        }

        .gallery__head {
            position: relative;
            width: 100%;
            height: 20vh;
            top: 0;
            left: 0;
            object-fit: contain;
            object-position: left;
        }

        svg.bounding_box {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        svg.bounding_box rect {
            fill: transparent;
        }

        svg.bounding_box rect.outer {
            stroke: black;
            stroke-width: 5px;
        }

        svg.bounding_box rect.inner {
            stroke: white;
            stroke-width: 3px;
        }

        .gallery__score {
            width: 100%;
            font-size: 2vw;
        }

        .gallery__prompts {
            width: 100%;
            font-size: 2vw;
        }

        .gallery__list {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            justify-content: space-around;
            margin: 10px 0 20px 0;
            row-gap: 20px; /* Space between rows */
        }

        .gallery__item {
            position: relative;
            width: calc(20% - 10px); /* Five items per row */
            height: 20vh;
            margin-bottom: 120px;
            text-align: center;
            border: 1px solid #ccc;
        }

        .gallery__item .gallery__detail {
            display: block;
            z-index: 1;
            color: #333;
            font-family: monospace;
            font-size: 14px;
        }

        .gallery__item img, .gallery__head img {
            display: block;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            object-fit: contain;
            object-position: center;
            user-select: none;
            pointer-events: none;
        }

        svg.watermark {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            fill: rgba(183, 197, 211);
            font-family: 'Work Sans', monospace;
            font-weight: bold;
            z-index: 2;
            user-select: none;
            pointer-events: none;
            display: none;
        }

        svg.watermark rect {
            fill: transparent;
            stroke: white;
            stroke-width: 5px;
        }

        svg.watermark text {
            dominant-baseline: middle;
            text-anchor: middle;
            user-select: none;
            pointer-events: none;
        }

        svg.watermark :nth-child(2) {
            font-size: 16px;
        }

        svg.watermark :last-child {
            font-size: 28px;
        }

        .zoomable:hover {
            opacity: .8;
        }

        #zoom {
            display: flex;
            visibility: hidden;
            background-color: #9999;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 100;
        }

        #zoom.active {
            visibility: visible;
            display: flex;
            opacity: 1;
        }

        #zoom div {
            position: relative;
            display: flex;
            max-height: 90%;
            max-width: 90%;
        }

        #zoom_image {
            max-width: 100%;
            max-height: 100%;
            margin: auto;
            object-fit: contain;
            object-position: center;
            user-select: none;
            pointer-events: none;
        }

        #zoom svg.watermark {
            z-index: 101;
            user-select: none;
            pointer-events: none;
        }

    </style>
</head>
<body>
<fieldset>
    <legend><h2>Provenance - C Check</h2></legend>

    <form id="image_c_check_form">
        <div id="drop_zone" class="box">
            <svg class="box__icon" xmlns="http://www.w3.org/2000/svg" width="50" height="43" viewBox="0 0 50 43">
                <path d="M48.4 26.5c-.9 0-1.7.7-1.7 1.7v11.6h-43.3v-11.6c0-.9-.7-1.7-1.7-1.7s-1.7.7-1.7 1.7v13.2c0 .9.7 1.7 1.7 1.7h46.7c.9 0 1.7-.7 1.7-1.7v-13.2c0-1-.7-1.7-1.7-1.7zm-24.5 6.1c.3.3.8.5 1.2.5.4 0 .9-.2 1.2-.5l10-11.6c.7-.7.7-1.7 0-2.4s-1.7-.7-2.4 0l-7.1 8.3v-25.3c0-.9-.7-1.7-1.7-1.7s-1.7.7-1.7 1.7v25.3l-7.1-8.3c-.7-.7-1.7-.7-2.4 0s-.7 1.7 0 2.4l10 11.6z"></path>
            </svg>
            <input type="file" id="image_c_check_image_source" class="box__file">
            <label for="image_c_check_image_source">
                <span>Choose C-Check Image or drag it here</span>
            </label>
            <img id="preview_image" class="box__img zoomable">
        </div>
        <center>
            <div>
                <br>
                <label for="prompts">Prompts (If empty, the default prompts: "cartoon character" will be used)</label>
                <br><bt>
                <input type="text" id="prompts" name="prompts" placeholder="Enter prompts here" width="70%">
            </div>
        </center>
        <button type="button" id="submit_image_c_check_button" class="btn">Submit</button>
    </form>
    <div id="results" class="gallery"></div>
</fieldset>

<div id="zoom" class="fullscreen-center">
    <div>
        <img id="zoom_image">
        <svg class="bounding_box">
        </svg>
        <svg class="watermark" viewBox="0 0 240 120" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="shadow" height="130%">
                    <feDropShadow dx=".2" dy="2" stdDeviation=".6" flood-color="white" />
                </filter>
            </defs>
            <text x="50%" y="39%"
                  transform="rotate(345 120 60)"
                  filter="url(#shadow)">
                COPYRIGHTED CONTENT
            </text>
            <text x="50%" y="61%"
                  transform="rotate(345 120 60)"
                  filter="url(#shadow)">
                Research Only
            </text>
        </svg>
    </div>
</div>

<div id="loader-container" class="fullscreen-center">
    <span class="loader"></span>
</div>

<script>
    const stage = "{{ stage }}";
    const service = "{{ service }}";
    const api_root_url = "{{ api_fqdn }}";

    function roundTo(num, decimal) {
        return Math.round( num * Math.pow(10, decimal)) / Math.pow(10, decimal);
    }

    document.addEventListener("DOMContentLoaded", function () {
        const form = document.getElementById("image_c_check_form");
        const imageInput = document.getElementById("image_c_check_image_source");
        const submitButton = document.getElementById("submit_image_c_check_button");

        imageInput.addEventListener('change', () => {
            setPreviewImage(imageInput.files[0]);
        });

        submitButton.addEventListener("click", function () {
            const divResults = document.getElementById('results')
            divResults.innerHTML = ''
            submitButton.disabled = true;
            const processingElement = document.getElementById("loader-container");
            processingElement.classList.add('active')

            const image = imageInput.files[0];

            // Resize iamge to 512*512 (keep aspect ratio) before uploading
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.onload = function () {
                const MAX_WIDTH = 1024;
                const MAX_HEIGHT = 1024;
                let width = img.width;
                let height = img.height;
                if (width > height) {
                    if (width > MAX_WIDTH) {
                        height *= MAX_WIDTH / width;
                        width = MAX_WIDTH;
                    }
                } else {
                    if (height > MAX_HEIGHT) {
                        width *= MAX_HEIGHT / height;
                        height = MAX_HEIGHT;
                    }
                }
                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob(blob => {
                    const formData = new FormData();
                    formData.append("image", blob);
                    const c_check_api_image_upload = `${api_root_url}/api/v1/image`
                    fetch(c_check_api_image_upload, {
                        method: "POST",
                        body: formData,
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            const image_id = data["image_id"];
                            const prompts = document.getElementById("prompts").value;
                            const c_check_api_image_search = `${api_root_url}/api/v1/image/${image_id}/demo?demo_type=research&prompts=${prompts}`
                            return fetch(c_check_api_image_search, {
                                method: "GET",
                            })
                                .then((response) => response.json())
                                .then((searchData) => {
                                    return searchData
                                })
                        })
                        .then((searchData) => {
                            drawImages(searchData["results"])
                        })
                        .catch((error) => {
                            console.error("Error:", error);
                        })
                        .finally(() => {
                            processingElement.classList.remove('active');
                            submitButton.disabled = false;
                        })
                }, 'image/jpeg', 0.75);
            }
            img.src = URL.createObjectURL(image);
        });
    });

    const dropZone = document.getElementById('drop_zone');
    const previewImage = document.getElementById('preview_image');
    let dragCounter = 0; // Counter to track entering and leaving drag items

    let setPreviewImage = (path) => {
        previewImage.src = URL.createObjectURL(path);
        previewImage.classList.add('active');
    }

    previewImage.onerror = () => {
        previewImage.classList.remove('active');
        previewImage.src = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA"
    }

    // Show drop zone when dragging files over the window
    dropZone.addEventListener('dragenter', function(e) {
        e.preventDefault();
        dragCounter++;
        dropZone.classList.add('dragging');
    });

    // Hide drop zone when leaving the window or dropping the file
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dragCounter--;
        if (dragCounter === 0) { // Only hide when all drags have left the window
            dropZone.classList.remove('dragging');
        }
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dragCounter = 0; // Reset counter on drop
        dropZone.classList.remove('dragging');
        if (e.dataTransfer.files.length) {
            const imageInput = document.getElementById("image_c_check_image_source");
            // To get first file only
            let dataTransfer = new DataTransfer();
            dataTransfer.items.add(e.dataTransfer.files[0]);
            imageInput.files = dataTransfer.files;
            setPreviewImage(imageInput.files[0]);
        }
    });

    // Prevent default drag behaviors
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
    });

    const zoomDiv = document.getElementById('zoom')
    const zoomImage = document.getElementById('zoom_image')
    const zoomBoundingSvg = zoomDiv.getElementsByClassName('bounding_box')[0]
    const zoomableUnits = document.getElementsByClassName('zoomable');

    zoomDiv.addEventListener('click', e => {
        e.preventDefault()
        zoomDiv.classList.remove('active')
    })

    Array.from(zoomableUnits).forEach((unit) => {
        bindZoomable(unit)
    })
    function bindZoomable(unit) {
        unit.addEventListener('click', e => {
            e.preventDefault()
            let source = (unit.tagName.toUpperCase() === 'IMG') ? unit : unit.getElementsByTagName('img')[0]
            zoomDiv.classList.add('active')
            zoomImage.src = source.src

            let pos = JSON.parse(source.getAttribute('pos')) || [0, 0]
            let boxes = JSON.parse(source.getAttribute('boxes')) || []
            zoomBoundingSvg.setAttribute('viewBox', `0 0 ${pos[0]} ${pos[1]}`)
            zoomBoundingSvg.innerHTML = boxes.map(box => {
                return `<rect class="outer"
                    x="${box[0]}"
                    y="${box[1]}"
                    width="${box[2] - box[0]}"
                    height="${box[3] - box[1]}" />
                    <rect class="inner"
                    x="${box[0]}"
                    y="${box[1]}"
                    width="${box[2] - box[0]}"
                    height="${box[3] - box[1]}" />`
            })
        })
    }

    async function drawImages(searchData) {
        const srcImage = await new Promise((resolve, reject) => {
            const img = new Image()
            img.onload = () => resolve(img)
            img.onerror = reject
            img.src = "data:image/png;base64," + searchData["fullFrame"]["imageBase64"]
        })

        const divResults = document.getElementById('results')

        searchData["fullFrame"]["srcPos"] = [srcImage.naturalWidth, srcImage.naturalHeight]
        for (const [f_r_index, item] of searchData.fullFrame.searchResult.entries()) {
            const bboxImage = await new Promise((resolve, reject) => {
                const img = new Image()
                img.onload = () => resolve(img)
                img.onerror = reject
                img.src = item.imageURL
            })
            searchData.fullFrame.searchResult[f_r_index]["box"] = searchData.fullFrame.searchResult[f_r_index]?.box?.map(b=>Math.round(b*100)/100) || []
            searchData.fullFrame.searchResult[f_r_index]["srcPos"] = [bboxImage.naturalWidth, bboxImage.naturalHeight]
        }

        image_search_data = {
            "fullFrame": searchData["fullFrame"],
        }

        for (const [key, value] of Object.entries(searchData["boundingBoxes"])) {
            for (const [b_r_index, item] of value.searchResult.entries()) {
                const bboxImage = await new Promise((resolve, reject) => {
                    const img = new Image()
                    img.onload = () => resolve(img)
                    img.onerror = reject
                    img.src = item.imageURL
                })
                item["box"] = item?.box?.map(b => Math.round(b * 100) / 100) || []
                item["srcPos"] = [bboxImage.naturalWidth, bboxImage.naturalHeight]
            }
            image_search_data["z"+key] = value
        }

        console.log(image_search_data)

        divResults.innerHTML = `
            <div class="gallery">
                ${Object.entries(image_search_data).map(([boundingKey, boundingResult]) => {
                    return `<div class="gallery__group">
                        <div class="gallery__head zoomable">
                            <img src="data:image/png;base64,${image_search_data[boundingKey].imageBase64}"
                                pos="${JSON.stringify(boundingResult.srcPos || [0, 0])}"
                                boxes="${JSON.stringify(boundingResult.boxes || [])}"
                                />
                            ${(boundingResult['boxes'] !== undefined && boundingResult['srcPos'] !== undefined) ?
                                `
                                <svg viewBox="0 0 ${boundingResult['srcPos'][0]} ${boundingResult['srcPos'][1]}"
                                    class="bounding_box"
                                    xmlns="http://www.w3.org/2000/svg"
                                    >
                                    ${boundingResult['boxes'].map(box => {
                                        return `<rect class="outer"
                                            x="${box[0]}"
                                            y="${box[1]}"
                                            width="${box[2]-box[0]}"
                                            height="${box[3]-box[1]}" />
                                            <rect class="inner"
                                            x="${box[0]}"
                                            y="${box[1]}"
                                            width="${box[2]-box[0]}"
                                            height="${box[3]-box[1]}" />`
                                    }).join('\n')}
                                </svg>
                                `
                            : '' }
                        </div>
                        ${boundingResult.prompts !== undefined ?
                            `
                            <span class="gallery__prompts">${boundingResult.prompts}</span>
                            `
                        : '' }

                        ${typeof boundingResult.score === 'number' ?
                            `
                            <span class="gallery__prompts">${boundingResult.prompt}</span>
                            <span class="gallery__score">${roundTo(boundingResult.score, 4)}</span>
                            `
                        : '' }
                        <div class="gallery__list">
                            ${boundingResult["searchResult"].map(item => {
                                return `
                                    <div class="gallery__item zoomable">
                                        <img src="${item.imageURL}"
                                            pos="${JSON.stringify(item['srcPos'] || [0, 0])}"
                                            boxes="[${JSON.stringify(item.box || [])}]"
                                            />
                                        <svg viewBox="0 0 ${item['srcPos'][0]} ${item['srcPos'][1]}"
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="bounding_box"
                                            >
                                            ${item.searchType === 'BBOX' ?
                                                `<rect class="outer"
                                                x="${item.box[0]}"
                                                y="${item.box[1]}"
                                                width="${item.box[2]-item.box[0]}"
                                                height="${item.box[3]-item.box[1]}" />
                                                <rect class="inner"
                                                x="${item.box[0]}"
                                                y="${item.box[1]}"
                                                width="${item.box[2]-item.box[0]}"
                                                height="${item.box[3]-item.box[1]}" />`
                                            : '' }
                                        </svg>
                                        <span class="gallery__detail">${item.brand}</span>
                                        <span class="gallery__detail">${item.title}</span>
                                        <span class="gallery__detail">Dist: ${roundTo(item.dist, 2)}</span>
                                        <span class="gallery__detail">Video ID: ${item.videoId}</span>
                                        <span class="gallery__detail">Frame ID: ${item.frameId}</span>
                                        ${typeof item.score === 'number' ?
                                            `
                                            <span class="gallery__detail">Score: ${roundTo(item.score, 4)}</span>
                                            <span class="gallery__detail">[Box]<br>[${item.box.join(', ')}]</span>
                                            `
                                        : ''}
                                        <svg class="watermark" viewBox="0 0 240 120" xmlns="http://www.w3.org/2000/svg">
                                            <defs>
                                                <filter id="shadow" height="130%">
                                                    <feDropShadow dx=".2" dy="2" stdDeviation=".6" flood-color="white" />
                                                </filter>
                                            </defs>
                                            <text x="50%" y="39%"
                                                transform="rotate(345 120 60)"
                                                filter="url(#shadow)">
                                                COPYRIGHTED CONTENT
                                            </text>
                                            <text x="50%" y="61%"
                                                transform="rotate(345 120 60)"
                                                filter="url(#shadow)">
                                                Research Only
                                            </text>
                                        </svg>
                                    </div>
                                `
                            }).join('')}
                        </div>
                    </div>`
                }).join('\n')}
            </div>
        `

        Array.from(divResults.getElementsByClassName('zoomable')).forEach(unit => {
            bindZoomable(unit)
        })
    }

</script>
</body>
</html>
