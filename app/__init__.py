import warnings
from logging.config import dictConfig

from app.common.logger_config import LogConfigHandler
from app.utils.lrucache import *

warnings.filterwarnings('ignore', category=UserWarning,
                        message='None of the inputs have requires_grad=True. Gradients will be None')
warnings.filterwarnings('ignore', category=UserWarning,
                        message='torch.meshgrid: in an upcoming release, it will be required to pass the indexing '
                                'argument.')
warnings.filterwarnings('ignore', category=UserWarning,
                        message='TypedStorage is deprecated')
warnings.filterwarnings('ignore', category=FutureWarning,
                        message='The `device` argument is deprecated and will be removed in v5 of Transformers.')

settings = get_settings()

if settings.log_path:
    os.makedirs(os.path.dirname(settings.log_path), exist_ok=True)

dictConfig(
    LogConfigHandler(
        _level=settings.log_level,
        _format=settings.log_format,
        _log_in_json=settings.log_in_json,
        _path=settings.log_path,
    ).model_dump()
)

"""
This will be the first line of logging in the application. <PERSON><PERSON> starts taking action at this point after 
initialization of LogConfigHandler.
"""
logger = logging.getLogger('main.app')
logger.info(f'App initializing | {json.dumps((settings.dict()), indent=2)}')

DEPENDENCIES_PATH = os.path.join(settings.workspace, 'app/dependencies/groundingdino')
sys.path.append(DEPENDENCIES_PATH)
