import json
import logging
import os
import sys
from functools import lru_cache

import boto3
from google.cloud import storage
from google.cloud.storage import Client
from redis.asyncio import Redis

from app.settings import Settings, SqsConfig, SqsConnector, RedisConfig, RedisConnector

logger = logging.getLogger('main.app')


@lru_cache(maxsize=1)
def get_settings():
    """
    Retrieves the application settings.

    This function determines the workspace directory by traversing up from the current file's directory until it finds
    the 'app' directory. It then sets the `WORKSPACE` environment variable and loads the settings from the `.env` file.

    If running in the Docker container, the `.env` file is expected to be in the workspace directory. Otherwise,
    `envs/local.env` is used.

    Returns:
        Settings: An instance of the Settings class populated with values from the `.env` file.
    """
    my_dir = os.path.dirname(os.path.realpath(__file__))
    while my_dir != '/':
        if os.path.basename(my_dir) == 'app':
            break
        my_dir = os.path.dirname(my_dir)

    workspace = os.path.dirname(my_dir)
    env_path = os.path.join(workspace, '.env')
    if not os.path.exists(env_path):
        env_path = os.path.join(workspace, 'envs/local.env')

    os.environ['WORKSPACE'] = workspace
    return Settings(_env_file=env_path)


@lru_cache(maxsize=0 if "pytest" in sys.modules else 256)
def get_gcp_storage_client() -> Client:
    path = f'{get_settings().workspace}/app/resources/secrets/gcp-secret.json'
    if not os.path.exists(path):
        raise SystemExit(f"gcp secret file not exist: {path}")

    return storage.Client.from_service_account_json(path)


@lru_cache(maxsize=0 if "pytest" in sys.modules else 256)
def get_aws_s3_client():
    path = f'{get_settings().workspace}/app/resources/secrets/aws-secret.json'
    if not os.path.exists(path):
        raise SystemExit(f"aws secret file not exist: {path}")

    with open(path, 'r') as f:
        aws_secret = json.load(f)

    return boto3.client(
        's3',
        aws_access_key_id=aws_secret['aws_access_key_id'],
        aws_secret_access_key=aws_secret['aws_secret_access_key'],
    )


@lru_cache(maxsize=0 if "pytest" in sys.modules else 256)
def get_env_tag_to_sqs_dict() -> dict[str, SqsConnector]:
    path = f'{get_settings().workspace}/app/resources/secrets/aws-secret.json'
    if not os.path.exists(path):
        raise SystemExit(f"aws secret file not exist: {path}")

    with open(path, 'r') as f:
        aws_secret = json.load(f)

    _dict = {}
    configs = [SqsConfig(**config) for config in json.loads(get_settings().sqs_json)]
    for config in configs:
        _dict[config.stage + config.tag] = SqsConnector(
            stage=config.stage,
            tag=config.tag,
            url=config.url,
            client=boto3.client(
                'sqs',
                region_name=config.region,
                aws_access_key_id=aws_secret['aws_access_key_id'],
                aws_secret_access_key=aws_secret['aws_secret_access_key'],
            )
        )
    return _dict


@lru_cache(maxsize=1)
def get_env_to_redis_dict() -> dict[str, RedisConnector]:
    _dict = {}
    configs = [RedisConfig(**config) for config in json.loads(get_settings().redis_json)]
    for config in configs:
        _dict[config.stage] = RedisConnector(
            stage=config.stage,
            source_client=Redis(
                host=config.source_host,
                port=config.source_port,
                socket_keepalive=True,
                health_check_interval=60,
                ssl=True
            ),
            feature_client=Redis(
                host=config.feature_host,
                port=config.feature_port,
                decode_responses=True,
                socket_keepalive=True,
                health_check_interval=60,
                ssl=True
            )
        )
    return _dict
