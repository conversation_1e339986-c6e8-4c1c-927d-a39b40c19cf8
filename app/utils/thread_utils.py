import asyncio
import os
from typing import Callable, Any

from app.common.thread_common import PrioritizedThreadPoolExecutor

_cpu_executor = PrioritizedThreadPoolExecutor.create(max_workers=max(os.cpu_count() - 1, 1))
_feature_executor = PrioritizedThreadPoolExecutor.create(max_workers=1)
_detection_executor = PrioritizedThreadPoolExecutor.create(max_workers=1)


async def run_cpu_task(priority: int, func: Callable[..., Any], *args: Any) -> Any:
    future = _cpu_executor.submit(priority, func, *args)
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: future.result())


async def run_feature_task(priority: int, func: Callable[..., Any], *args: Any) -> Any:
    future = _feature_executor.submit(priority, func, *args)
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: future.result())


async def run_detection_task(priority: int, func: Callable[..., Any], *args: Any) -> Any:
    future = _detection_executor.submit(priority, func, *args)
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: future.result())
