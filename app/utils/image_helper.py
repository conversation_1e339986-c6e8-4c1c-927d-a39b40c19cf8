import asyncio
import io

import numpy as np
import pillow_avif
import pillow_heif
from PIL import Image

from app import get_settings
from app.common.logger_common import log_exception
from app.storage.base_cloud_storage import add_mimetype
from app.storage.s3_storage import S3Storage
from app.utils.thread_utils import run_cpu_task

pillow_heif.register_heif_opener()
_ = pillow_avif.__version__

C_CHECK_STORAGE_BUCKET = 'provenance-c-check-api-storage-us-east-1'
C_CHECK_STORAGE = S3Storage(C_CHECK_STORAGE_BUCKET)

SEGMENT_EXTRACTOR = 'extractor'
SEGMENT_THROUGHPUT = 'throughput'
SEGMENT_THUMBNAIL = 'thumbnail'
THROUGHPUT_SEGMENTS = f'{SEGMENT_EXTRACTOR}/{get_settings().stage}/{SEGMENT_THROUGHPUT}'
THUMBNAIL_SEGMENTS = f'{SEGMENT_EXTRACTOR}/{get_settings().stage}/{SEGMENT_THUMBNAIL}'

"""
The b00 file extension is used to store the bounding box features in binary format. Changes in the
concatenate_box_features function should reflect the file extension to b01, b02, etc.
"""
BIN_EXT = 'dat'
FEATURE_BIN_EXT = 'b00'
add_mimetype(f'.{BIN_EXT}', 'application/octet-stream')
add_mimetype(f'.{FEATURE_BIN_EXT}', 'application/octet-stream')

MAX_IMAGE_QUALITY = 100
THUMBNAIL_WEBP_QUALITY = 50
CONST_THUMBNAIL_LONG_SIDE = 360


class CloudPath:
    def __init__(self, env: str, session_id: str, priority: int = 0):
        self.env = env
        self.session_id = session_id
        self.serial = 0
        self.priority = priority

    @property
    def throughput_anchor(self) -> str:
        serial_hex = '{:02x}'.format(self.serial)
        return f'{THROUGHPUT_SEGMENTS}/{self.env}/{self.session_id}/{serial_hex}'

    @property
    def thumbnail_anchor(self) -> str:
        serial_hex = '{:02x}'.format(self.serial)
        return f'{THUMBNAIL_SEGMENTS}/{self.env}/{self.session_id}/{serial_hex}'

    @property
    def key(self) -> str:
        return f's3://{C_CHECK_STORAGE_BUCKET}/{THROUGHPUT_SEGMENTS}/{self.env}/{self.session_id}'

    def upload_box_features_bin_no_wait(self, data: bytes):
        C_CHECK_STORAGE.upload_no_wait(
            path=f'{self.throughput_anchor}.{FEATURE_BIN_EXT}',
            data=data,
        )

    def upload_unsupported_img_no_wait(self, img_data: bytes):
        C_CHECK_STORAGE.upload_no_wait(
            path=f'{self.throughput_anchor}.{BIN_EXT}',
            data=img_data,
        )

    def upload_mask_no_wait(self, mask: Image):
        buffer = io.BytesIO()
        mask.save(buffer, format='PNG')
        C_CHECK_STORAGE.upload_no_wait(
            path=f'{self.throughput_anchor}-mask.png',
            data=buffer.getvalue(),
        )

    async def handle_img(self, img: Image, img_data: bytes) -> (str, bytes):
        if img.format:
            ext1 = img.format.lower()
        else:
            ext1 = 'webp'
            img_data = await run_cpu_task(self.priority, convert_to_webp, img)

        img_path = f'{self.throughput_anchor}.{ext1}'
        C_CHECK_STORAGE.upload_no_wait(img_path, img_data)
        return C_CHECK_STORAGE.to_uri(img_path), img_data

    async def handle_thumbnail(self, img: Image, img_data: bytes) -> str:
        is_big = len(img_data) > 1000 * 20  # 20KB
        if is_big or not img.format:
            ext2 = 'webp'
            thumbnail_data = await run_cpu_task(self.priority, create_thumbnail, img)
        else:
            ext2 = img.format.lower()
            thumbnail_data = img_data

        thumbnail_path = f'{self.thumbnail_anchor}.{ext2}'
        await asyncio.to_thread(C_CHECK_STORAGE.upload_data, thumbnail_path, thumbnail_data)
        return C_CHECK_STORAGE.to_uri(thumbnail_path)


def convert_to_webp(img):
    buffer = io.BytesIO()
    img.save(buffer, format='WEBP', quality=MAX_IMAGE_QUALITY)
    return buffer.getvalue()


def create_thumbnail(img):
    scale = CONST_THUMBNAIL_LONG_SIDE / max(img.width, img.height)
    scaled_img = img.resize((int(img.width * scale), int(img.height * scale)))
    buffer = io.BytesIO()
    scaled_img.save(buffer, format='WEBP', quality=THUMBNAIL_WEBP_QUALITY)
    return buffer.getvalue()


def rgba_to_rgb(img):
    rgba = np.array(img)
    rgb = np.zeros((rgba.shape[0], rgba.shape[1], 3), dtype=np.uint8)

    mask = rgba[:, :, 3] > 0
    rgb[mask] = rgba[mask][:, :3]

    new_img = Image.fromarray(rgb)
    return new_img


def prep_img(img_data, uri) -> Image:
    try:
        img = Image.open(io.BytesIO(img_data))
        if img.mode == 'RGBA':
            img = rgba_to_rgb(img)
        elif img.mode != 'RGB' or img.format == 'PNG':
            img = img.convert('RGB')
        return img

    except Exception as e:
        log_exception(tag=f'PrepImg | {uri}')
        return None


def get_masked_image(img: Image, mask: Image, th=0.25) -> Image:
    bbox = mask.getbbox()
    if bbox is None:
        return None

    bbox_width = bbox[2] - bbox[0]
    bbox_height = bbox[3] - bbox[1]
    bbox_area = bbox_width * bbox_height

    image_width, image_height = mask.size
    image_area = image_width * image_height

    area_ratio = bbox_area / image_area
    if area_ratio < th:
        return None

    rgb_array = np.array(img)
    mask_array = np.array(mask)

    blackout_mask = (mask_array == 0)
    rgb_array[blackout_mask] = [0, 0, 0]

    return Image.fromarray(rgb_array)
