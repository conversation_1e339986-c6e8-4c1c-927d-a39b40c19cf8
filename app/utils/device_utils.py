import logging
import os
import platform
import sys

import torch

logger = logging.getLogger("main.app")


def get_fn_base(fn):
    return os.path.splitext(os.path.basename(fn))[0]


def is_mac():
    return 'darwin' in sys.platform


def is_mac_arm():
    return 'arm' in platform.processor()


def get_device_str(gpu):
    device_str = 'cpu'

    if type(gpu) is int:
        if gpu >= 0:
            if is_mac() and is_mac_arm():
                device_str = 'mps'
            if torch.cuda.is_available():
                device_str = 'cuda:%d' % (gpu % torch.cuda.device_count())

    elif type(gpu) is str:
        device_str = gpu

    elif isinstance(gpu, list):
        device_str = ['cuda:%d' % (i) for i in gpu]

    else:
        raise ValueError('gpu can be an integer, a string or list of integers')

    return device_str


def get_device(gpu: int):
    device_str = get_device_str(gpu)
    return torch.device(device_str)
