import logging

from http import HTT<PERSON>tatus
from http.client import HTT<PERSON><PERSON>x<PERSON>

from app.exceptions.error_code import ErrorCode


logger = logging.getLogger('main.app')


class ServiceException(HTTPException):
    def __init__(self, status_code: HTTPStatus, error: str, detail: str):
        super().__init__(status_code)

        self.status_code = status_code
        self.error = error
        self.detail = detail

    def __str__(self):
        return f'{self.__class__.__name__}(status_code={self.status_code}, error={self.error}, detail={self.detail})'

    @classmethod
    def from_error_code(cls, error_code: ErrorCode, message: str = None, e: Exception = None,):
        logger.error(f'message: {message}, error: {e}')
        return ServiceException(
            status_code=error_code.status,
            error=error_code.error,
            detail=error_code.detail
        )
