from enum import Enum
from http import HTTPStatus


class ErrorCode(Enum):
    INVALID_SERIAL_0 = (
        HTTPStatus.BAD_REQUEST,
        'Serial 0 is reserved for AIGC images'
    )
    INVALID_SERIAL = (
        HTTPStatus.BAD_REQUEST,
        'Serial is in the range of 1 to 99'
    )
    INVALID_ENV = (
        HTTPStatus.BAD_REQUEST,
        'Allowed provenance.local.dev only'
    )
    INVALID_URI = (
        HTTPStatus.BAD_REQUEST,
        'Failed to download the image'
    )
    UNSUPPORTED_IMAGE_FORMAT = (
        HTTPStatus.BAD_REQUEST,
        'Unsupported image format'
    )
    EXTRACTION_ERROR = (
        HTTPStatus.INTERNAL_SERVER_ERROR,
        'Feature extraction failed'
    )
    SERVER_BUSY_ERROR = (
        HTTPStatus.INTERNAL_SERVER_ERROR,
        'Too many requests'
    )

    def __init__(self, status: HTTPStatus, detail: str):
        self.status: HTTPStatus = status
        self.error: str = status.name
        self.detail: str = detail
