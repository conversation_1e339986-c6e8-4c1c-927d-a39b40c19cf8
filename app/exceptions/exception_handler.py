import logging
import sys
import traceback
from http import HTT<PERSON>tatus
from typing import Union

from fastapi import Request
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError, HTTPException
from fastapi.utils import is_body_allowed_for_status_code
from starlette.responses import J<PERSON><PERSON>esponse, Response

from app.exceptions.service_exception import ServiceException

logger = logging.getLogger("main.app")


# Reference: https://fastapi.tiangolo.com/tutorial/handling-errors/
class ExceptionHandler:

    @classmethod
    async def validation_exception_handler(cls, request: Request, exc: RequestValidationError) -> JSONResponse:
        """
        This is a wrapper to the default RequestValidationException handler of FastAPI.
        This function will be called when client input is not valid.
        """
        # return await request_validation_exception_handler(request, exc)

        return JSONResponse(
            status_code=HTTPStatus.UNPROCESSABLE_ENTITY,
            content={'detail': jsonable_encoder(exc.errors()), 'message': 'Input is invalid'},
        )

    @classmethod
    async def http_request_exception_handler(cls, request: Request, exc: HTTPException) -> Union[JSONResponse, Response]:
        """
        This is a wrapper to the default HTTPException handler of FastAPI.
        This function will be called when a HTTPException is explicitly raised.
        """
        # return await http_exception_handler(request, exc)

        headers = getattr(exc, 'headers', None)
        if not is_body_allowed_for_status_code(exc.status_code):
            return Response(status_code=exc.status_code, headers=headers)
        return JSONResponse(
            {'detail': exc.detail, 'message': exc.detail}, status_code=exc.status_code, headers=headers
        )

    @classmethod
    async def service_exception_handler(cls, request: Request, exc: ServiceException) -> Union[JSONResponse, Response]:
        """
        This is the exception handler for this service which handling service exception class.
        This function will be called when a ServiceException is explicitly raised.
        """
        mapping = vars(exc)
        mapping['message'] = exc.detail

        return JSONResponse(
            status_code=exc.status_code,
            content=mapping
        )

    @classmethod
    async def unhandled_exception_handler(cls, request: Request, exc: Exception) -> JSONResponse:
        """
        This middleware will log all unhandled exceptions.
        Unhandled exceptions are all exceptions that are not HTTPExceptions or RequestValidationErrors.
        """
        host = getattr(getattr(request, 'client', None), 'host', None)
        port = getattr(getattr(request, 'client', None), 'port', None)
        url = f'{request.url.path}?{request.query_params}' if request.query_params else request.url.path
        exception_type, exception_value, exception_traceback = sys.exc_info()
        tb_frame = exception_traceback.tb_frame

        exception_name = getattr(exception_type, '__name__', None)
        # Extract traceback information
        tb = traceback.extract_tb(exception_traceback)

        # Get the last call in the traceback with /app/
        for t in tb[::-1]:
            if "/app/" in t.filename:
                last_call = t
                break

        # Extract the relevant information
        exception_file = last_call.filename if last_call else "Unknown"
        exception_line = last_call.lineno if last_call else "Unknown"
        exception_func = last_call.name if last_call else "Unknown"

        logger.error(
            f"{exception_name}: {exc}",
            # extra is used to add additional fields to the log record for
            # structured logging in GCP Log Explorer
            extra={
                'logging.googleapis.com/sourceLocation': {
                    'file': exception_file,
                    'line': exception_line,
                    'function': exception_func
                }
            }
        )

        return JSONResponse(
            content={'detail': str(exc), 'message': 'There was an error processing your request. Please try again later.'},
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )
