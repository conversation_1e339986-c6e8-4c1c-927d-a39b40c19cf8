from http import HTTPStatus

import numpy as np
from fastapi_camelcase import CamelModel
from pydantic import Field

from app import SqsConnector
from app.common.logger_common import log_exception
from app.exceptions.error_code import ErrorCode


class ExtractionDto(CamelModel):
    score: float
    ratio: float
    boxes: list[int]
    features: list[float]


class ExtractFactor(CamelModel):
    stage: str
    env: str
    session_id: str
    uri: str
    min_box: float = 1.0
    box_prompts: str = 'cartoon character'
    extraction: list[ExtractionDto] = []
    image_uri: str = ''
    thumbnail_uri: str = ''
    status: int = HTTPStatus.OK.value
    error_code: str = ''
    error_message: str = ''

    @property
    def key(self):
        return f'{self.session_id} | {self.uri}'

    @property
    def is_ok(self):
        return self.status == HTTPStatus.OK

    @property
    def is_error(self):
        return self.status != HTTPStatus.OK

    def concatenate_bin_data(self) -> bytes:
        features = []
        for e in self.extraction:
            bs = np.array(e.boxes, dtype=np.uint16).tobytes()
            fs = np.array(e.features, dtype=np.float32).tobytes()
            features.append(bs)
            features.append(fs)
        return b''.join(features)

    def set_error(self, error: ErrorCode):
        self.status = error.status.value
        self.error_code = error.name
        self.error_message = error.detail
        log_exception(tag=f'{error.name} | {self.key}')


class ExtractMessage(CamelModel):
    env: str
    session_id: str
    uri: str
    serial: int | None = Field(default=None)
    prompts: str = Field(default='')
    min_box: float = Field(default=1.0)

    @property
    def is_facade_item(self) -> bool:
        return self.serial is None

    def to_extract_factor(self, sqs_connector: SqsConnector) -> ExtractFactor:
        return ExtractFactor(
            stage=sqs_connector.stage,
            env=self.env,
            session_id=self.session_id,
            uri=self.uri,
        )
