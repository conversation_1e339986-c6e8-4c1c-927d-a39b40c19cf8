import asyncio
import logging
from abc import abstractmethod
from urllib.parse import urlparse

from PIL import Image

from app import get_env_to_redis_dict
from app.common.http_async_builder import HttpAsyncBuilder
from app.common.logger_common import TimedContext, timed_context
from app.exceptions.error_code import ErrorCode
from app.schemas.features import ExtractFactor, ExtractionDto
from app.services.model_initor import FEATURE_EXTRACTOR, DINO_DETECTOR
from app.storage.gcp_storage import GcpStorage
from app.storage.s3_storage import S3Storage
from app.utils.image_helper import CloudPath, prep_img
from app.utils.thread_utils import run_cpu_task

logger = logging.getLogger('main.app')
logger.info(f'Init {__file__}')

MSG_TTL_SEC: int = 60 * 15

FACADE_PRIORITY = 1
LENS_PRIORITY = 10


class Session:
    def __init__(self, ext_factor: ExtractFactor, priority: int):
        self.factor = ext_factor
        self.path = CloudPath(ext_factor.env, ext_factor.session_id, priority)
        self.priority = priority

    @property
    def key(self):
        return f'{self.factor.session_id}/{self.path.serial}'

    async def start(self):
        await self._exec(_alt_ctx_tag='end')

    @timed_context
    async def _exec(self, _alt_ctx_tag):
        img, img_data = await self.download_n_convert()
        if self.factor.is_ok:
            await self._fork(img, img_data)

        await self.to_redis()
        TimedContext.log(f'{self.path.key} | {[x.boxes for x in self.factor.extraction]}')

    @abstractmethod
    async def _fork(self, img: Image, img_data: bytes):
        pass

    @timed_context
    async def download_n_convert(self) -> tuple[Image, bytes] | tuple[None, None]:
        parsed = urlparse(self.factor.uri)
        scheme = parsed.scheme
        bucket = parsed.hostname
        path = parsed.path.lstrip('/')

        handlers = {
            'gs': lambda: GcpStorage(bucket).download_async(path),
            'gcs': lambda: GcpStorage(bucket).download_async(path),
            's3': lambda: S3Storage(bucket).download_async(path),
            'redis': lambda: get_env_to_redis_dict()[self.factor.stage].get_source_binary_message(self.factor.uri)
        }
        handler = handlers.get(scheme)
        if handler:
            img_data = await handler()
        else:
            response = await HttpAsyncBuilder(self.factor.uri, tag='Download').enable_follow_redirects().execute()
            img_data = response.content if response is not None else None

        if img_data is None:
            self.factor.set_error(ErrorCode.INVALID_URI)
            return None, None

        img = await run_cpu_task(self.priority, prep_img, img_data, self.factor.uri)
        if img is None:
            self.factor.set_error(ErrorCode.UNSUPPORTED_IMAGE_FORMAT)
            return None, None

        short_side = min(img.width, img.height)
        if short_side > 1024:
            scale = 1024.0 / short_side
            width = int(img.width * scale)
            height = int(img.height * scale)
            img = await run_cpu_task(self.priority, img.resize, (width, height))
            img_data = img.tobytes()
            TimedContext.log(f'resize {scale} -> {width, height}')

        return img, img_data

    @timed_context
    async def to_redis(self):
        redis_connector = get_env_to_redis_dict()[self.factor.stage]
        await redis_connector.write_message(self.key, MSG_TTL_SEC, self.factor.model_dump())

    @timed_context
    async def extract(self, img: Image, img_data: bytes):
        np_array = await FEATURE_EXTRACTOR.extract(img, [], self.priority)

        if len(np_array) == 0:
            self.path.upload_unsupported_img_no_wait(img_data)
            self.factor.set_error(ErrorCode.UNSUPPORTED_IMAGE_FORMAT)
            return

        features = np_array.tolist()[0]
        self.factor.extraction.insert(0, ExtractionDto(
            score=-1.0,
            ratio=1.0,
            boxes=[0, 0, int(img.width), int(img.height)],
            features=features,
        ))

    @timed_context
    async def extract_boxes(self, img: Image):
        boxes = await DINO_DETECTOR.detect(img, self.priority, self.factor.box_prompts, self.factor.min_box)
        if len(boxes) == 0:
            return

        boxes_features = await asyncio.gather(*[
            FEATURE_EXTRACTOR.extract(img, box_dict['box'], self.priority) for box_dict in boxes
        ])

        for index, (box_dict, features) in enumerate(zip(boxes, boxes_features)):
            pos = index + 1
            box = box_dict['box']
            self.factor.extraction.insert(pos, ExtractionDto(
                score=box_dict['score'],
                ratio=box_dict['ratio'],
                boxes=box,
                features=features.tolist()[0] if len(features) > 0 else [],
            ))


class FacadeSession(Session):
    def __init__(self, ext_factor: ExtractFactor, min_box: float, box_prompts: str):
        super().__init__(ext_factor, FACADE_PRIORITY)
        self.factor.min_box = min_box
        self.factor.box_prompts = box_prompts
        self._priority = FACADE_PRIORITY
        TimedContext.tag.set(f'XFacade | {self.key}')
        TimedContext.log(f'start | {self.factor.uri} | min_box={min_box}, box_prompts={box_prompts}')

    async def _fork(self, img: Image, img_data: bytes):
        img_task = self.extract(img, img_data)
        box_task = self.extract_boxes(img)
        await asyncio.gather(img_task, box_task, return_exceptions=True)
        if self.factor.is_ok:
            self.path.upload_box_features_bin_no_wait(self.factor.concatenate_bin_data())


class LensSession(Session):
    def __init__(self, ext_factor: ExtractFactor, serial: int):
        super().__init__(ext_factor, LENS_PRIORITY)
        self.path.serial = serial
        TimedContext.tag.set(f'XLens | {self.key}')
        TimedContext.log(f'start | {self.factor.uri} | serial={serial}')

    async def _fork(self, img: Image, img_data: bytes):
        await self.extract(img, img_data)
        if self.factor.is_ok:
            self.factor.image_uri, handled_img_data = await self.path.handle_img(img, img_data)
            self.factor.thumbnail_uri = await self.path.handle_thumbnail(img, handled_img_data)
