from __future__ import annotations

import logging
import time

import numpy as np
import torch
from PIL import Image, ImageFile

from transformers import AutoProcessor, AutoModelForZeroShotObjectDetection

from app.common.logger_common import log_exception, timed
from app.utils.device_utils import get_device, get_fn_base
from app.utils.lrucache import get_settings
from app.utils.thread_utils import run_detection_task

logger = logging.getLogger('main.app')
logger.info(f'Init {__file__}')

ImageFile.LOAD_TRUNCATED_IMAGES = True


class DinoDetector(object):
    def __init__(
            self,
            gpu_id: int = 0,
            model_id: str = 'IDEA-Research/grounding-dino-base',
            box_th = 0.4,
            text_th = 0.3,
            prompts_str: str = 'cartoon character.'
    ):
        self.device = get_device(gpu_id)
        self.box_th = box_th
        self.text_th = text_th
        self.processor = AutoProcessor.from_pretrained(model_id)
        self.model = AutoModelForZeroShotObjectDetection.from_pretrained(model_id).to(self.device)

        # get default prompts
        self.prompts_str = prompts_str

    async def detect(self, im: Image, priority: int, ratio_min=0.25, ratio_max=0.99) -> list[dict]:
        return await run_detection_task(priority, self._detect, im, ratio_min, ratio_max)

    def _detect(self, im, ratio_min=0.25, ratio_max=0.99) -> list[dict]:
        bbox_score_results = list()
        try:
            start_time = time.time()
            inputs = self.processor(images=im, text=self.prompt_str, return_tensors="pt").to(self.device)
            with torch.no_grad():
                outputs = self.model(**inputs)

            self.log_detect_elapsed('bbox detection elapsed', start_time)

            det = self.processor.post_process_grounded_object_detection(
                outputs,
                inputs.input_ids,
                box_threshold=self.box_th,
                text_threshold=self.text_th,
                target_sizes=[im.size[::-1]]
            )

            scores = det[0]['scores'].cpu().tolist()
            boxes = det[0]['boxes'].cpu().tolist()

            # Save box image for each box
            area = im.width * im.height
            for items in zip(boxes, scores):
                box, score = items
                ratio = (box[2] - box[0]) * (box[3] - box[1]) / area
                if ratio_min <= ratio <= ratio_max:
                    bbox_score_results.append({
                        'box': box,
                        'score': score,
                        'ratio': ratio
                    })

        except Exception as e:
            log_exception('DinoDetector:detect')

        bbox_score_results.sort(key=lambda x: x['ratio'], reverse=True)
        return bbox_score_results

    @staticmethod
    def log_detect_elapsed(tag, start_time):
        logger.debug(f'DinoDetector:detect - {tag} elapsed: {time.time() - start_time}')


@timed
def init_image_segmenter():
    if get_settings().is_local:
        logger.debug('Local mode - skip init_image_segmenter')
        return None

    if not get_settings().feature_extractor_model_path:
        raise RuntimeError('Not set: feature_extractor_model_path')

    if not torch.cuda.is_available():
        logger.info('GPU is not available for init_image_segmenter')

    detector = DinoDetector(
        gpu_id=0,
        groundingdino_model=get_settings().image_segmenter_model_id,
        box_th=0.4,
        text_th=0.3,
        prompts_str='cartoon character.'
    )
    return detector
