import concurrent.futures

from app.common.logger_common import timed
from app.services.feature_extractor.feature_extractor import init_feature_extractor
from app.services.image_segmenter.dino_detector import init_image_segmenter


@timed
def initialize_models():
    with concurrent.futures.ThreadPoolExecutor() as executor:
        feature_extractor = executor.submit(init_feature_extractor)
        dino_detector = executor.submit(init_image_segmenter)
        f = feature_extractor.result()
        d = dino_detector.result()
    return f, d


FEATURE_EXTRACTOR, DINO_DETECTOR = initialize_models()
