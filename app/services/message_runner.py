import asyncio
import json

from app import get_env_tag_to_sqs_dict, get_env_to_redis_dict
from app.common.logger_common import log_exception, logger
from app.schemas.features import ExtractMessage
from app.services.session import FacadeSession, LensSession
from app.settings import SqsConnector, RedisConnector

env_tag_to_sqs_dict: dict[str, SqsConnector] = get_env_tag_to_sqs_dict()
env_redis_dict: dict[str, RedisConnector] = get_env_to_redis_dict()


class Poller:
    def __init__(self, sqs_connector: SqsConnector):
        self.sqs_connector = sqs_connector
        self._stop = asyncio.Event()

    async def start(self):
        logger.info(f'{self.sqs_connector.log_tag}Start')
        while not self._stop.is_set():
            sqs_response = await asyncio.to_thread(self.sqs_connector.receive_message)
            msgs = sqs_response.get('Messages', [])
            if msgs:
                tasks = [MessageRunner.process_message(self.sqs_connector, msg) for msg in msgs]
                await asyncio.gather(*tasks)

    def stop(self):
        logger.info(f'{self.sqs_connector.log_tag}Stop')
        self._stop.set()


class MessageRunner:
    _pollers: list[Poller] = [Poller(v) for v in env_tag_to_sqs_dict.values()]

    @staticmethod
    def start():
        logger.info(f'MessageRunner start: {len(MessageRunner._pollers)}')
        for p in MessageRunner._pollers:
            asyncio.create_task(p.start())

    @staticmethod
    def stop():
        for p in MessageRunner._pollers:
            p.stop()

    @staticmethod
    async def process_message(sqs_connector: SqsConnector, msg: dict):
        msg_id = msg['MessageId']
        msg_handle = msg['ReceiptHandle']
        body = json.loads(msg['Body'])

        try:
            ext_msg = ExtractMessage(**body)
            if ext_msg.is_facade_item:
                session = FacadeSession(ext_msg.to_extract_factor(sqs_connector), ext_msg.min_box, ext_msg.prompts)
            else:
                session = LensSession(ext_msg.to_extract_factor(sqs_connector), ext_msg.serial)
            asyncio.create_task(session.start())
        except Exception:
            log_exception(f'{sqs_connector.log_tag}Process | {msg_id} | {body}')

        try:
            await asyncio.to_thread(sqs_connector.delete_message, msg_handle)
        except Exception:
            log_exception(f'{sqs_connector.log_tag}Delete | {msg_id}')
