from __future__ import annotations

import logging
import os
import threading
from typing import Optional

import numpy as np
import torch
import torch.nn as nn
from PIL import Image
from torch.nn import functional as tf

from app.common.logger_common import log_exception, timed
from app.utils.lrucache import get_settings
from app.utils.thread_utils import run_feature_task, run_cpu_task

logger = logging.getLogger('main.app')
logger.info(f'Init {__file__}')

if not get_settings().is_local:
    from app.services.feature_extractor.eva_clip import (
        CLIPVisionCfg,
        _build_vision_tower,
        get_cast_dtype,
        get_model_config,
        image_transform,
        load_checkpoint,
    )

# ===============================
OPENAI_DATASET_MEAN = (0.48145466, 0.4578275, 0.40821073)
OPENAI_DATASET_STD = (0.26862954, 0.26130258, 0.27577711)

_extract_lock = threading.Lock()


class CustomCLIPVision(nn.Module):
    def __init__(
            self,
            embed_dim: int,
            vision_cfg: CLIPVisionCfg,
            quick_gelu: bool = False,
            cast_dtype: Optional[torch.dtype] = None):
        super().__init__()
        self.visual = _build_vision_tower(embed_dim, vision_cfg, quick_gelu, cast_dtype)

        self.visual.image_mean = OPENAI_DATASET_MEAN
        self.visual.image_std = OPENAI_DATASET_STD

    def encode_image(self, image, normalize: bool = False):
        features = self.visual(image)
        return tf.normalize(features, dim=-1) if normalize else features


class ImageFeatureExtractor(object):
    def __init__(
            self,
            gpu_id: int = 0,
            model_path: str = 'EVA02_CLIP_E_psz14_plus_s9B_trimmed.pt',
            record_enabled: bool = False
    ):
        self.record_enabled = record_enabled

        with torch.no_grad(), torch.cuda.amp.autocast():
            if self.record_enabled: torch.cuda.memory._record_memory_history()

            # clean cache BEFORE feature extractor loaded
            torch.cuda.empty_cache()

            self.device = torch.device(f'cuda:{gpu_id}' if torch.cuda.is_available() else 'cpu')
            # model_name = 'EVA02-CLIP-bigE-14-plus'
            # pretrained = 'eva_clip' # or '/path/to/EVA02_CLIP_B_psz16_s8B.pt'
            model_name = 'EVA02-CLIP-bigE-14-plus'
            # model_path = 'EVA02_CLIP_E_psz14_plus_s9B_trimmed.pt'
            precision = 'fp16'

            self.model, self.preprocess = ImageFeatureExtractor.load_model(model_name, model_path, precision)
            dtype = torch.float32
            if precision in ['fp16', 'bf16']:
                dtype = torch.bfloat16 if precision == 'bf16' else torch.float16
            self.model = self.model.to(dtype=dtype).to(device=self.device)
            self.model.eval()

            # clean cache AFTER feature extractor loaded
            torch.cuda.empty_cache()

            if self.record_enabled:
                torch.cuda.memory._dump_snapshot('/opt/gpu_memory_records/eva_model_loading.pickle')

    @staticmethod
    def load_model(model_name, model_path, precision):
        with torch.no_grad(), torch.cuda.amp.autocast():
            model_name = model_name.replace('/', '-')  # for callers using old naming with / in ViT names
            model_cfg = get_model_config(model_name)
            # {'embed_dim': 1024
            # 'vision_cfg': {'image_size': 224
            #                 'layers': 64
            #                 'width': 1792
            #                 'head_width': 112
            #                 'mlp_ratio': 8.571428571428571
            #                 'patch_size': 14
            #                 'eva_model_name': 'eva-clip-4b-14-x'
            #                 'drop_path_rate': 0
            #                 'xattn': True
            #                 'postnorm': True
            #                 'fusedLN': True}
            # }
            assert model_cfg is not None, f'Model config for {model_name} not found'
            del model_cfg['text_cfg']

            os.environ['RoPE'] = '0'
            model = CustomCLIPVision(**model_cfg, cast_dtype=get_cast_dtype(precision))

            load_checkpoint(model, model_path, model_key='model|module|state_dict', strict=False)

            preprocess_val = image_transform(model.visual.image_size,
                                             is_train=False,
                                             mean=model.visual.image_mean,
                                             std=model.visual.image_std)
            return model, preprocess_val

    async def extract(self, img: Image, crop_box: list[int], priority: int) -> np.array:
        if crop_box:
            img = await run_cpu_task(priority, img.crop, crop_box)
        return await run_feature_task(priority, self._extract, img)

    def _extract(self, img: Image) -> np.array:
        try:
            tensor = self.preprocess(img).unsqueeze(0).to(self.device)

            with torch.no_grad(), torch.cuda.amp.autocast():
                # clean cache BEFORE feature extraction
                torch.cuda.empty_cache()

                image_features = self.model.encode_image(tensor)
                image_features /= image_features.norm(dim=-1, keepdim=True)

                # clean cache AFTER feature extraction
                torch.cuda.empty_cache()

            return image_features.cpu().float().numpy()

        except Exception:
            log_exception('ImageFeatureExtractor::extract')
            return np.array([])


@timed
def init_feature_extractor():
    if get_settings().is_local:
        logger.debug('Local mode - skip init_feature_extractor')
        return None

    if not get_settings().feature_extractor_model_path:
        raise RuntimeError('Not set: feature_extractor_model_path')

    if not torch.cuda.is_available():
        logger.info('GPU is not available for init_feature_extractor')

    return ImageFeatureExtractor(
        gpu_id=0,
        model_path=get_settings().feature_extractor_model_path,
        record_enabled=get_settings().gpu_memory_record_enabled
    )
