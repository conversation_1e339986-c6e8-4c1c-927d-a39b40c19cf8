import logging

import torch
from PIL import Image
from torchvision import transforms
from transformers import AutoModelForImageSegmentation

from app import get_settings
from app.common.logger_common import log_exception, timed

logger = logging.getLogger('main.app')
logger.info(f'Init {__file__}')


class BirefNet:
    def __init__(self, device_id: int = 0, size=(512, 512), use_fp16: bool = False):
        self.device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() else "cpu")

        self.trans = transforms.Compose([
            transforms.Resize(size),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])

        self.model = AutoModelForImageSegmentation.from_pretrained('ZhengPeng7/BiRefNet', trust_remote_code=True)

        self.use_fp16 = use_fp16
        if self.use_fp16:
            self.model.half()
        else:
            torch.set_float32_matmul_precision(['high', 'highest'][0])

        self.model.to(self.device)
        self.model.eval()

    def remove_bg(self, img: Image) -> Image:
        if self.use_fp16:
            tensor = self.trans(img).unsqueeze(0).half().to(self.device)
        else:
            tensor = self.trans(img).unsqueeze(0).to(self.device)

        with torch.no_grad():
            preds = self.model(tensor)[-1].sigmoid().cpu()

        pred = preds[0].squeeze()
        pred_pil = transforms.ToPILImage()(pred)
        mask = pred_pil.resize(img.size)

        return mask


@timed
def init_background_remover():
    if get_settings().is_local:
        logger.debug('Local mode - skip init_background_remover')
        return None

    if not get_settings().feature_extractor_model_path:
        raise RuntimeError('Not set: init_background_remover')

    try:
        return BirefNet()
    except Exception:
        log_exception('init_background_remover')
        return None
