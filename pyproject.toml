[tool.poetry]
name = "provenance-c-check"
version = "0.1.12"
description = ""
authors = ["LWH"]
readme = "README.md"

[tool.poetry.dependencies]
apex = "^0.9.10.dev0"
boto3 = "^1.34.19"
deepspeed = "^0.14.4"
einops = "^0.8.0"
faiss-cpu = "^1.7.4"
fastapi = "^0.108.0"
fastapi-camelcase = "^2.0.0"
fastapi-profiler = "^1.2.0"
ftfy = "^6.2.0"
google-cloud-storage = "^2.14.0"
gunicorn = "^21.2.0"
httpx = "^0.26.0"
jinja2 = "^3.1.2"
kornia = "^0.8.0"
loguru = "^0.7.2"
numpy = "^1.26.3"
opencv-python = "^********"
pillow = "11.2.1"
pillow-avif-plugin = "1.5.2"
pillow-heif = "0.22.0"
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
pyramid = "1.5"
python = "^3.10"
python-json-logger = "^2.0.7"
redis = "^5.2.1"
supervision = "^0.3.2"
timm = "^0.9.12"
torch = "==2.1.1"
torchinfo = "^1.8.0"
torchvision = "^0.16.0"
transformers = "==4.48.0"
uvicorn = "^0.25.0"

[tool.poetry.dev-dependencies]
pytest = "^8.0.0"
pylint = "^3.0.3"
locust = "^2.23.1"
pytest-cov = "^5.0.0"
pytest-asyncio = "^0.23.7"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
