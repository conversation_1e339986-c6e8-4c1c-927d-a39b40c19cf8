# To be confirmed. No actual usage. The latest Dockerfile exists in the CD pipeline.
#
#version: '3.7'
#
#services:
#  c-check-local:
#    image: c-check:develop
#    container_name: c-check-local
#    platform: linux/amd64
#    environment:
#      - WORKSPACE=/opt
#    command: >
#      gunicorn app.main:app -w 1 -k uvicorn.workers.UvicornWorker --timeout 3600 -b 0.0.0.0:8003 --limit-request-line 0 --limit-request-field_size 0 --log-level debug -c /opt/gunicorn_conf.py
#    volumes:
#      - ${HOME}/.config:/root/.config
#      - ./app:/opt/app
#      - ./envs/local.env:/opt/.env
#      - ./configs:/opt/configs
#      - ./gunicorn_conf.py:/opt/gunicorn_conf.py
#      - ./faiss_indices:/opt/faiss_indices
#      - ./logs:/opt/logs
#      - ./tests:/opt/tests
#    ports:
#      - "8003:8003"
#    networks:
#      - local
#
#networks:
#  local:
