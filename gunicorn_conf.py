import requests
import time

next_chk_delay = 30

server_port = 8003
helthcheck_endpoint = f"http://localhost:{server_port}/chk"

def pre_fork(server, worker):
    # Use the number of workers that have been started to determine the delay
    worker_index = len(server.WORKERS)

    print(worker.ppid)
    print(server.WORKERS)

    if worker_index == 0:
        print("This is the master worker, no delay needed")
        return

    else:
        start_to_launch = False

        while not start_to_launch:
            # Check if the service chk endpoint is ready
            try:
                response = requests.get(
                    helthcheck_endpoint,
                    timeout=10
                )
                if response.status_code == 200:
                    start_to_launch = True
                    print(f"Worker {worker_index + 1} is ready to launch")
                    break
            except Exception as e:
                # if time out retry
                if "timed out" in str(e):
                    print(f"Worker {worker_index + 1} is not ready, retrying in {next_chk_delay} seconds")
                    time.sleep(next_chk_delay)

                    for w in server.WORKERS:
                        print(w, server.WORKERS[w].__dir__())
