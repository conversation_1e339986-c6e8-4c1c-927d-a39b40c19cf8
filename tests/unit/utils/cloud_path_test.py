import io
import unittest
from unittest.mock import patch, MagicMock
from urllib.parse import urlparse

from PIL import Image

from app import get_settings
from app.storage.base_cloud_storage import get_mimetype
from app.utils.image_helper import CloudPath, C_CHECK_STORAGE, C_CHECK_STORAGE_BUCKET, SEGMENT_EXTRACTOR, \
    SEGMENT_THROUGHPUT, SEGMENT_THUMBNAIL, FEATURE_BIN_EXT, BIN_EXT


class TestCloudPath(unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        self.cloud_path = CloudPath('env', 'session_id')

    def test_mimetypes(self):
        self.assertEqual(get_mimetype(f'gs://bucket/folder/file.{BIN_EXT}'), 'application/octet-stream')
        self.assertEqual(get_mimetype(f'gs://bucket/folder/file.{FEATURE_BIN_EXT}'), 'application/octet-stream')

        self.assertEqual(get_mimetype('gs://bucket/folder/file.png'), 'image/png')
        self.assertEqual(get_mimetype('gs://bucket/folder/file.jpg'), 'image/jpeg')
        self.assertEqual(get_mimetype('gs://bucket/folder/file.jpeg'), 'image/jpeg')
        self.assertEqual(
            get_mimetype('extractor/dev/thumbnail/provenance.local.dev/ba6cfae0-28b5-450a-abd7-c4ee94610237/04.webp'),
            'image/webp')

    @patch('asyncio.create_task')
    def test_upload_box_features_bin(self, mock_create_task):
        data = b'data'
        C_CHECK_STORAGE.upload_no_wait = MagicMock()
        self.cloud_path.upload_box_features_bin_no_wait(data)
        self.assertEqual(C_CHECK_STORAGE.upload_no_wait.call_count, 1)

    @patch('asyncio.create_task')
    def test_upload_unsupported_img(self, mock_create_task):
        data = b'data'
        C_CHECK_STORAGE.upload_no_wait = MagicMock()
        self.cloud_path.upload_unsupported_img_no_wait(data)
        self.assertEqual(C_CHECK_STORAGE.upload_no_wait.call_count, 1)

    @patch('asyncio.create_task')
    async def test_upload_img_and_thumbnail_small_image(self, mock_create_task):
        img = Image.new('RGB', (10, 10))
        img_data = io.BytesIO()
        img.save(img_data, format='PNG')
        img_data = img_data.getvalue()

        C_CHECK_STORAGE.upload_no_wait = MagicMock()
        img_uri, handled_img_data = await self.cloud_path.handle_img(img, img_data)
        thumbnail_uri = await self.cloud_path.handle_thumbnail(img, handled_img_data)

        self.examine_path(img_uri, SEGMENT_THROUGHPUT, 0, 'webp')
        self.examine_path(thumbnail_uri, SEGMENT_THUMBNAIL, 0, 'webp')
        self.assertEqual(C_CHECK_STORAGE.upload_no_wait.call_count, 1)

    @patch('asyncio.create_task')
    async def test_upload_img_and_thumbnail_large_image(self, mock_create_task):
        img = Image.new('RGB', (5000, 5000))
        img_data = io.BytesIO()
        img.save(img_data, format='PNG')
        img_data.seek(0)
        img = Image.open(img_data)
        img_data = img_data.getvalue()

        C_CHECK_STORAGE.upload_no_wait = MagicMock()
        img_uri, handled_img_data = await self.cloud_path.handle_img(img, img_data)
        thumbnail_uri = await self.cloud_path.handle_thumbnail(img, handled_img_data)

        self.examine_path(img_uri, SEGMENT_THROUGHPUT, 0, 'png')
        self.examine_path(thumbnail_uri, SEGMENT_THUMBNAIL, 0, 'webp')
        self.assertEqual(C_CHECK_STORAGE.upload_no_wait.call_count, 1)

    def examine_path(self, path: str, type_segment: str, serial: int, ext: str):
        parsed = urlparse(path)
        scheme = parsed.scheme
        bucket = parsed.hostname
        path = parsed.path.lstrip('/').split('/')
        self.assertEqual(scheme, 's3')
        self.assertEqual(bucket, C_CHECK_STORAGE_BUCKET)
        self.assertEqual(path[0], SEGMENT_EXTRACTOR)
        self.assertEqual(path[1], get_settings().stage)
        self.assertEqual(path[2], type_segment)
        self.assertEqual(path[3], self.cloud_path.env)
        self.assertEqual(path[4], self.cloud_path.session_id)
        self.assertTrue(path[5].startswith('{:02x}'.format(serial)))
        self.assertTrue(path[5].endswith(ext))
