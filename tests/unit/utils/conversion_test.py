import json

from app.schemas.features import ExtractMessage


def test_message_service_categorize_msgs():
    src = '{"env":"provenance.local.dev","uri":"gs://provenance-aigc-storage/provenance.local.dev/tests/lion.png","session_id":"65e00408-60e7-4620-b5e8-93ca99808cd6","prompts":"cartoon character","min_box":0.25}'
    body = json.loads(src)
    dto = ExtractMessage(**body)
    assert dto.is_facade_item
