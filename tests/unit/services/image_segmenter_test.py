from app.services.image_segmenter.dino_detector import DinoDetector
from PIL import Image

if __name__ == '__main__':
    detector = DinoDetector(
        gpu_id=0,
        model_id='IDEA-Research/grounding-dino-base',
        box_th=0.4,
        text_th=0.3,
        prompts_str='cartoon character.'
    )

    img = Image.open('/home/<USER>/ccheck/test_data/test8.webp')
    boxes = detector.detect(img, 0)
    print(boxes)