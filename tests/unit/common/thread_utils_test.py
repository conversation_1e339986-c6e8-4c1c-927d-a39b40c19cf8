import threading
import time
import unittest

from app.common.logger_common import to_utc_iso_time_ms
from app.common.thread_common import PrioritizedThreadPoolExecutor


class TestPrioritizedThreadPoolExecutor(unittest.TestCase):
    def test_task_priority_execution_order(self):
        results = []
        lock = threading.Lock()

        def make_task(label, delay):
            def task():
                time.sleep(delay)
                with lock:
                    results.append((label, to_utc_iso_time_ms(time.time())))
                return label

            return task

        executor = PrioritizedThreadPoolExecutor.create(max_workers=1)

        f1 = executor.submit(3, make_task('task_low', 0.1))
        f2 = executor.submit(1, make_task('task_high', 0.5))
        f3 = executor.submit(2, make_task('task_mid', 0.3))

        f1.result()
        f2.result()
        f3.result()
        executor.shutdown()

        print(results)

        sorted_by_time_results = sorted(results, key=lambda x: x[1])
        executed_task_labels = [i[0] for i in sorted_by_time_results]

        self.assertEqual(executed_task_labels, ['task_high', 'task_mid', 'task_low'])
