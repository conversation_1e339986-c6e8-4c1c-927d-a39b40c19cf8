## Process Flow
1. Subscribe from Redis Pubsub to start polling from SQS
2. Poll messages from SQS
3. Perform feature extraction
4. Write results to Redis

## Deployment
- Dev EC2 
  - ************* (main1), 10.120.27.163 (main2)
  - Manual deployment
    1. Build image on <PERSON> - https://jenkins.provenance.ai/job/provenance-c-check-extractor-image-build/
    2. Obtain the image tag from https://us-east-1.console.aws.amazon.com/ecr/repositories/private/572111738119/c-check-extractor?region=us-east-1
    3. Login to EC2 - ssh ubuntu@*************
    4. Deploy with the command - `bash deploy.sh <image_tag>`
  - Logs & Debug
    - Docker bash - `docker exec -it c-check-dev /bin/bash`
    - Docker log - `docker logs --tail 100 -f c-check-dev`
    - App log - `tail -f /var/log/provenance-c-check/app.log`
    - Python libs - `pip list`
  - Remove old images
    - `docker images | grep "572111738119.dkr.ecr.us-east-1.amazonaws.com/c-check-extractor" | awk '{print $3}' | xargs -I {} docker rmi -f {}`
    
- Prod Auto Scaling Group 
  - https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#AutoScalingGroupDetails:id=extractor-ecs-asg;view=details
  - Deployment via Jenkins - https://jenkins.provenance.ai/job/provenance-c-check-extractor-Release/
